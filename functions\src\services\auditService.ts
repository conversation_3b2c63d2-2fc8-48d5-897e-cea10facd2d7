import * as admin from 'firebase-admin';

const db = admin.firestore();

interface AuditLogData {
  action: string;
  userId: string;
  orderId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
}

class AuditService {
  async logAction(data: AuditLogData) {
    try {
      await db.collection('logs').add({
        ...data,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error logging audit action:', error);
    }
  }

  async getAuditLogs(filters: {
    userId?: string;
    orderId?: string;
    action?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }) {
    let query = db.collection('logs') as any;

    if (filters.userId) {
      query = query.where('userId', '==', filters.userId);
    }
    if (filters.orderId) {
      query = query.where('orderId', '==', filters.orderId);
    }
    if (filters.action) {
      query = query.where('action', '==', filters.action);
    }
    if (filters.startDate) {
      query = query.where('createdAt', '>=', filters.startDate);
    }
    if (filters.endDate) {
      query = query.where('createdAt', '<=', filters.endDate);
    }

    query = query.orderBy('createdAt', 'desc');
    
    if (filters.limit) {
      query = query.limit(filters.limit);
    }

    const snapshot = await query.get();
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }
}

export const auditService = new AuditService();
