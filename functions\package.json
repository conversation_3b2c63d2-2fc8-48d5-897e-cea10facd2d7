{"name": "verto-order-functions", "version": "1.0.0", "description": "Firebase Functions for Verto Order Management System", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0", "express": "^4.18.2", "cors": "^2.8.5", "googleapis": "^131.0.0", "nodemailer": "^6.9.8", "twilio": "^4.19.3"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/nodemailer": "^6.4.14", "typescript": "^5.3.3"}, "private": true}