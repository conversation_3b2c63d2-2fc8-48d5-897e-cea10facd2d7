<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-purple-600 rounded-md flex items-center justify-center">
              <span class="text-white font-bold text-sm">V</span>
            </div>
            <span class="ml-2 text-xl font-semibold text-gray-900">Verto</span>
            <span class="ml-2 text-sm text-gray-500">Admin Dashboard</span>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-gray-700">{{ user?.name }}</span>
            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">Admin</span>
            <button @click="logout" class="text-blue-600 hover:text-blue-800">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Tab Navigation -->
    <div class="bg-white border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm',
              activeTab === tab.id
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            {{ tab.icon }} {{ tab.name }}
          </button>
        </nav>
      </div>
    </div>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        
        <!-- Overview Tab -->
        <div v-if="activeTab === 'overview'">
          <!-- Stats Grid -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Total Orders</h3>
              <p class="text-3xl font-bold text-blue-600">{{ allOrders.length }}</p>
              <p class="text-sm text-gray-500">All time</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Pending Approval</h3>
              <p class="text-3xl font-bold text-orange-600">{{ pendingCount }}</p>
              <p class="text-sm text-gray-500">Needs attention</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Active Clients</h3>
              <p class="text-3xl font-bold text-green-600">{{ activeClients }}</p>
              <p class="text-sm text-gray-500">This month</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Revenue</h3>
              <p class="text-3xl font-bold text-purple-600">${{ totalRevenue.toLocaleString() }}</p>
              <p class="text-sm text-gray-500">This month</p>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">📊 Recent Activity</h2>
            </div>
            <div class="p-6">
              <div class="space-y-4">
                <div v-for="activity in recentActivity" :key="activity.id" class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                  <div class="flex-shrink-0">
                    <span class="text-2xl">{{ activity.icon }}</span>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">{{ activity.title }}</p>
                    <p class="text-sm text-gray-500">{{ activity.description }}</p>
                  </div>
                  <div class="text-sm text-gray-400">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Orders Management Tab -->
        <div v-if="activeTab === 'orders'">
          <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 class="text-xl font-semibold text-gray-900">📋 All Orders Management</h2>
              <div class="flex space-x-3">
                <button @click="exportCSV" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                  📊 Export CSV
                </button>
                <button @click="previewCSV" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                  👁️ Preview CSV
                </button>
              </div>
            </div>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bags</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="order in allOrders" :key="order.id" :class="{ 'opacity-50 bg-red-50': order.status === 'deleted' }">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ order.id }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.clientName }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.potatoType }} - {{ order.cutShape }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.bags }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(order.deliveryDate) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getStatusClass(order.status)" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                        {{ order.status }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button @click="viewOrderDetails(order)" class="text-blue-600 hover:text-blue-900">View</button>
                      <button @click="editOrder(order)" class="text-green-600 hover:text-green-900">Edit</button>
                      <button v-if="order.status !== 'deleted'" @click="deleteOrder(order)" class="text-red-600 hover:text-red-900">Delete</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Reports Tab -->
        <div v-if="activeTab === 'reports'">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- CSV Preview -->
            <div class="bg-white rounded-lg shadow">
              <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">📄 CSV Preview</h2>
              </div>
              <div class="p-6">
                <div class="bg-gray-50 rounded-lg p-4 font-mono text-sm overflow-x-auto">
                  <div v-for="(line, index) in csvPreview" :key="index" class="whitespace-nowrap">
                    {{ line }}
                  </div>
                </div>
                <div class="mt-4 flex space-x-3">
                  <button @click="downloadCSV" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    💾 Download Full CSV
                  </button>
                  <button @click="refreshPreview" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    🔄 Refresh Preview
                  </button>
                </div>
              </div>
            </div>

            <!-- Analytics -->
            <div class="bg-white rounded-lg shadow">
              <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">📈 Analytics</h2>
              </div>
              <div class="p-6 space-y-4">
                <div class="border-l-4 border-blue-500 pl-4">
                  <h4 class="font-semibold text-gray-900">Order Trends</h4>
                  <p class="text-sm text-gray-600">{{ orderTrends }}</p>
                </div>
                <div class="border-l-4 border-green-500 pl-4">
                  <h4 class="font-semibold text-gray-900">Top Products</h4>
                  <p class="text-sm text-gray-600">{{ topProducts }}</p>
                </div>
                <div class="border-l-4 border-purple-500 pl-4">
                  <h4 class="font-semibold text-gray-900">Client Activity</h4>
                  <p class="text-sm text-gray-600">{{ clientActivity }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'

const router = useRouter()
const authStore = useSimpleAuthStore()
const { user } = authStore

// Tab management
const activeTab = ref('overview')
const tabs = [
  { id: 'overview', name: 'Overview', icon: '📊' },
  { id: 'orders', name: 'Orders Management', icon: '📋' },
  { id: 'reports', name: 'Reports & CSV', icon: '📄' }
]

// Sample data
const allOrders = ref([
  {
    id: '001',
    clientName: 'Restaurant ABC',
    potatoType: 'russet',
    cutShape: 'cubes',
    bags: 50,
    deliveryDate: '2024-01-15',
    status: 'approved',
    notes: 'For weekend rush',
    createdAt: '2024-01-10',
    price: 250
  },
  {
    id: '002',
    clientName: 'Cafe XYZ',
    potatoType: 'red',
    cutShape: 'wedges',
    bags: 25,
    deliveryDate: '2024-01-20',
    status: 'pending',
    notes: '',
    createdAt: '2024-01-12',
    price: 125
  },
  {
    id: '003',
    clientName: 'Hotel DEF',
    potatoType: 'yukon',
    cutShape: 'long',
    bags: 100,
    deliveryDate: '2024-01-18',
    status: 'completed',
    notes: 'Regular order',
    createdAt: '2024-01-08',
    price: 500
  },
  {
    id: '004',
    clientName: 'Restaurant ABC',
    potatoType: 'russet',
    cutShape: 'cubes',
    bags: 30,
    deliveryDate: '2024-01-25',
    status: 'deleted',
    notes: 'Cancelled by client',
    createdAt: '2024-01-13',
    price: 150
  }
])

const recentActivity = ref([
  {
    id: 1,
    icon: '📦',
    title: 'New Order Received',
    description: 'Restaurant ABC placed order #005 for 75 bags',
    time: '2 hours ago'
  },
  {
    id: 2,
    icon: '✅',
    title: 'Order Approved',
    description: 'Sales Manager approved order #002',
    time: '4 hours ago'
  },
  {
    id: 3,
    icon: '🚚',
    title: 'Order Delivered',
    description: 'Order #003 delivered to Hotel DEF',
    time: '1 day ago'
  }
])

// Computed properties
const pendingCount = computed(() => allOrders.value.filter(o => o.status === 'pending').length)
const activeClients = computed(() => new Set(allOrders.value.filter(o => o.status !== 'deleted').map(o => o.clientName)).size)
const totalRevenue = computed(() => allOrders.value.filter(o => o.status === 'completed').reduce((sum, o) => sum + o.price, 0))

const csvPreview = computed(() => {
  const headers = 'Order ID,Client,Potato Type,Cut Shape,Bags,Delivery Date,Status,Price,Notes'
  const rows = allOrders.value.slice(0, 5).map(order => 
    `${order.id},${order.clientName},${order.potatoType},${order.cutShape},${order.bags},${order.deliveryDate},${order.status},${order.price},"${order.notes}"`
  )
  return [headers, ...rows, '...']
})

const orderTrends = computed(() => `${allOrders.value.length} total orders, ${pendingCount.value} pending approval`)
const topProducts = computed(() => 'Russet Cubes (45%), Red Wedges (25%), Yukon Long (20%)')
const clientActivity = computed(() => `${activeClients.value} active clients this month`)

// Methods
const logout = () => {
  authStore.logout()
  router.push('/')
}

const viewOrderDetails = (order: any) => {
  alert(`Order #${order.id} Details:\nClient: ${order.clientName}\nProduct: ${order.potatoType} - ${order.cutShape}\nQuantity: ${order.bags} bags\nDelivery: ${order.deliveryDate}\nStatus: ${order.status}\nPrice: $${order.price}\nNotes: ${order.notes}`)
}

const editOrder = (order: any) => {
  alert(`Edit order #${order.id} - Admin override functionality`)
}

const deleteOrder = (order: any) => {
  if (confirm(`Are you sure you want to delete order #${order.id}? It will be marked as deleted but remain in records.`)) {
    order.status = 'deleted'
    alert('Order marked as deleted')
  }
}

const exportCSV = () => {
  generateCSV()
}

const previewCSV = () => {
  activeTab.value = 'reports'
}

const downloadCSV = () => {
  generateCSV()
}

const refreshPreview = () => {
  alert('CSV preview refreshed!')
}

const generateCSV = () => {
  const headers = 'Order ID,Client,Potato Type,Cut Shape,Bags,Delivery Date,Status,Price,Notes,Created Date'
  const rows = allOrders.value.map(order =>
    `${order.id},"${order.clientName}","${order.potatoType}","${order.cutShape}",${order.bags},${order.deliveryDate},${order.status},${order.price},"${order.notes}",${order.createdAt}`
  )

  const csvContent = [headers, ...rows].join('\n')
  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `verto-orders-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)

  alert('CSV file downloaded! 📊')
}

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    completed: 'bg-blue-100 text-blue-800',
    deleted: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}
</script>
