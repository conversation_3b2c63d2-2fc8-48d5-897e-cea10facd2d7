<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-blue-600 rounded-md flex items-center justify-center">
              <span class="text-white font-bold text-sm">V</span>
            </div>
            <span class="ml-2 text-xl font-semibold text-gray-900">Verto</span>
            <span class="ml-2 text-sm text-gray-500">Sales Manager</span>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-gray-700">{{ user?.name }}</span>
            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Sales Manager</span>
            <button @click="logout" class="text-blue-600 hover:text-blue-800">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        
        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Pending Approval</h3>
            <p class="text-3xl font-bold text-orange-600">{{ pendingOrders.length }}</p>
            <p class="text-sm text-gray-500">Requires action</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Approved Today</h3>
            <p class="text-3xl font-bold text-green-600">{{ approvedToday }}</p>
            <p class="text-sm text-gray-500">Orders processed</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Total Orders</h3>
            <p class="text-3xl font-bold text-blue-600">{{ allOrders.length }}</p>
            <p class="text-sm text-gray-500">All time</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Revenue Impact</h3>
            <p class="text-3xl font-bold text-purple-600">${{ pendingRevenue.toLocaleString() }}</p>
            <p class="text-sm text-gray-500">Pending approval</p>
          </div>
        </div>

        <!-- Pending Orders - Priority Section -->
        <div class="bg-white rounded-lg shadow mb-8">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">🚨 Orders Requiring Approval</h2>
            <p class="text-sm text-gray-600 mt-1">Review and approve/reject pending orders</p>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Details</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="order in pendingOrders" :key="order.id" class="hover:bg-yellow-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
                    <div class="text-sm text-gray-500">{{ formatDate(order.createdAt) }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ order.clientName }}</div>
                    <div class="text-sm text-gray-500">{{ order.clientType || 'Restaurant' }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ order.potatoType }} - {{ order.cutShape }}</div>
                    <div class="text-sm text-gray-500">{{ order.bags }} bags</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ formatDate(order.deliveryDate) }}</div>
                    <div class="text-sm" :class="getUrgencyClass(order.deliveryDate)">{{ getUrgency(order.deliveryDate) }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">${{ order.price }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button @click="approveOrder(order)" 
                            class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs font-medium">
                      ✅ Approve
                    </button>
                    <button @click="rejectOrder(order)" 
                            class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium">
                      ❌ Reject
                    </button>
                    <button @click="viewOrderDetails(order)" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium">
                      👁️ Details
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
            <div v-if="pendingOrders.length === 0" class="text-center py-8 text-gray-500">
              🎉 No pending orders! All caught up.
            </div>
          </div>
        </div>

        <!-- All Orders Management -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900">📋 All Orders Management</h2>
            <div class="flex space-x-3">
              <select v-model="statusFilter" class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="completed">Completed</option>
                <option value="deleted">Deleted</option>
              </select>
              <button @click="exportApprovedCSV" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                📊 Export Approved
              </button>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="order in filteredOrders" :key="order.id" :class="{ 'opacity-50 bg-red-50': order.status === 'deleted' }">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ order.id }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.clientName }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.potatoType }} - {{ order.cutShape }} ({{ order.bags }} bags)</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(order.deliveryDate) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getStatusClass(order.status)" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                      {{ order.status }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button @click="viewOrderDetails(order)" class="text-blue-600 hover:text-blue-900">View</button>
                    <button @click="overrideOrder(order)" class="text-purple-600 hover:text-purple-900">Override</button>
                    <button v-if="order.status !== 'deleted'" @click="flagOrder(order)" class="text-orange-600 hover:text-orange-900">Flag</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'

const router = useRouter()
const authStore = useSimpleAuthStore()
const { user } = authStore

const statusFilter = ref('')

// Sample orders data
const allOrders = ref([
  {
    id: '001',
    clientName: 'Restaurant ABC',
    clientType: 'Restaurant',
    potatoType: 'russet',
    cutShape: 'cubes',
    bags: 50,
    deliveryDate: '2024-01-15',
    status: 'approved',
    notes: 'For weekend rush',
    createdAt: '2024-01-10',
    price: 250
  },
  {
    id: '002',
    clientName: 'Cafe XYZ',
    clientType: 'Cafe',
    potatoType: 'red',
    cutShape: 'wedges',
    bags: 25,
    deliveryDate: '2024-01-20',
    status: 'pending',
    notes: '',
    createdAt: '2024-01-12',
    price: 125
  },
  {
    id: '005',
    clientName: 'Hotel DEF',
    clientType: 'Hotel',
    potatoType: 'yukon',
    cutShape: 'long',
    bags: 75,
    deliveryDate: '2024-01-16',
    status: 'pending',
    notes: 'Urgent order',
    createdAt: '2024-01-14',
    price: 375
  },
  {
    id: '003',
    clientName: 'Hotel DEF',
    clientType: 'Hotel',
    potatoType: 'yukon',
    cutShape: 'long',
    bags: 100,
    deliveryDate: '2024-01-18',
    status: 'completed',
    notes: 'Regular order',
    createdAt: '2024-01-08',
    price: 500
  }
])

// Computed properties
const pendingOrders = computed(() => allOrders.value.filter(o => o.status === 'pending'))
const approvedToday = computed(() => allOrders.value.filter(o => 
  o.status === 'approved' && new Date(o.createdAt).toDateString() === new Date().toDateString()
).length)
const pendingRevenue = computed(() => pendingOrders.value.reduce((sum, o) => sum + o.price, 0))

const filteredOrders = computed(() => {
  if (!statusFilter.value) return allOrders.value
  return allOrders.value.filter(o => o.status === statusFilter.value)
})

// Methods
const logout = () => {
  authStore.logout()
  router.push('/')
}

const approveOrder = (order: any) => {
  if (confirm(`Approve order #${order.id} from ${order.clientName}?`)) {
    order.status = 'approved'
    alert(`✅ Order #${order.id} approved successfully!`)
  }
}

const rejectOrder = (order: any) => {
  const reason = prompt(`Reject order #${order.id}. Please provide a reason:`)
  if (reason) {
    order.status = 'rejected'
    order.rejectionReason = reason
    alert(`❌ Order #${order.id} rejected: ${reason}`)
  }
}

const overrideOrder = (order: any) => {
  const action = prompt(`Override order #${order.id}. Enter new status (approved/rejected/completed/deleted):`)
  if (action && ['approved', 'rejected', 'completed', 'deleted'].includes(action)) {
    order.status = action
    alert(`🔧 Order #${order.id} status overridden to: ${action}`)
  }
}

const flagOrder = (order: any) => {
  const flag = prompt(`Flag order #${order.id}. Enter flag reason (problem/returned/quality_issue):`)
  if (flag) {
    order.flag = flag
    alert(`🚩 Order #${order.id} flagged: ${flag}`)
  }
}

const viewOrderDetails = (order: any) => {
  alert(`Order #${order.id} Details:\nClient: ${order.clientName} (${order.clientType})\nProduct: ${order.potatoType} - ${order.cutShape}\nQuantity: ${order.bags} bags\nDelivery: ${order.deliveryDate}\nStatus: ${order.status}\nPrice: $${order.price}\nNotes: ${order.notes}\nCreated: ${order.createdAt}`)
}

const exportApprovedCSV = () => {
  const approvedOrders = allOrders.value.filter(o => o.status === 'approved')
  const headers = 'Order ID,Client,Client Type,Potato Type,Cut Shape,Bags,Delivery Date,Price,Notes,Created Date'
  const rows = approvedOrders.map(order => 
    `${order.id},"${order.clientName}","${order.clientType}","${order.potatoType}","${order.cutShape}",${order.bags},${order.deliveryDate},${order.price},"${order.notes}",${order.createdAt}`
  )
  
  const csvContent = [headers, ...rows].join('\n')
  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `verto-approved-orders-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
  
  alert('Approved orders CSV downloaded! 📊')
}

const getUrgency = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days <= 1) return 'URGENT'
  if (days <= 3) return 'Soon'
  return `${days} days`
}

const getUrgencyClass = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days <= 1) return 'text-red-600 font-bold'
  if (days <= 3) return 'text-orange-600'
  return 'text-gray-500'
}

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    completed: 'bg-blue-100 text-blue-800',
    deleted: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}
</script>
