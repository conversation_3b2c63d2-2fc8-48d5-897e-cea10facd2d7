export type OrderStatus = 'pending' | 'approved' | 'rejected' | 'delivered' | 'cancelled'

export interface Product {
  id: string
  name: string
  description?: string
  unit: string // kg, tons, bags, etc.
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface OrderItem {
  productId: string
  productName: string
  quantity: number
  unit: string
  notes?: string
}

export interface Order {
  id: string
  clientId: string
  clientName: string
  clientBusinessName?: string
  clientEmail: string
  clientPhone?: string
  
  items: OrderItem[]
  totalQuantity: number
  
  status: OrderStatus
  requestedDeliveryDate: Date
  actualDeliveryDate?: Date
  
  clientNotes?: string
  internalNotes?: string // Only visible to factory staff
  rejectionReason?: string
  
  // Approval workflow
  approvedBy?: string
  approvedAt?: Date
  reviewedBy?: string
  reviewedAt?: Date
  
  // Timestamps
  createdAt: Date
  updatedAt: Date
  
  // Metadata
  orderNumber: string
  priority?: 'low' | 'medium' | 'high'
  tags?: string[]
  
  // Integration fields (for future WhatsApp integration)
  source?: 'web' | 'whatsapp' | 'phone' | 'email'
  originalMessage?: string
}

export interface OrderFilters {
  status?: OrderStatus[]
  clientId?: string
  dateFrom?: Date
  dateTo?: Date
  productId?: string
  priority?: string[]
  tags?: string[]
  search?: string
}

export interface OrderStats {
  total: number
  pending: number
  approved: number
  rejected: number
  delivered: number
  cancelled: number
}

export interface CreateOrderData {
  items: Omit<OrderItem, 'productName'>[]
  requestedDeliveryDate: Date
  clientNotes?: string
}

export interface UpdateOrderData {
  items?: OrderItem[]
  requestedDeliveryDate?: Date
  clientNotes?: string
  status?: OrderStatus
  internalNotes?: string
  rejectionReason?: string
  priority?: 'low' | 'medium' | 'high'
  tags?: string[]
}
