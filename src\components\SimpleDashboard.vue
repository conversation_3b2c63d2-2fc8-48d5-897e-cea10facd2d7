<template>
  <div class="min-h-screen bg-gray-100">
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-blue-600 rounded-md flex items-center justify-center">
              <span class="text-white font-bold text-sm">V</span>
            </div>
            <span class="ml-2 text-xl font-semibold text-gray-900">Verto</span>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-gray-700">Welcome, {{ user?.name || 'User' }}!</span>
            <span class="text-sm text-gray-500 capitalize">{{ user?.role?.replace('_', ' ') }}</span>
            <button @click="logout" class="text-blue-600 hover:text-blue-800">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <div class="border-4 border-dashed border-gray-200 rounded-lg p-8">
          <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Dashboard</h1>
            <p class="text-gray-600 mb-6">Welcome to the Verto Order System Dashboard</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Orders</h3>
                <p class="text-3xl font-bold text-blue-600">24</p>
                <p class="text-sm text-gray-500">Active orders</p>
              </div>
              <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Clients</h3>
                <p class="text-3xl font-bold text-green-600">12</p>
                <p class="text-sm text-gray-500">Active clients</p>
              </div>
              <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Revenue</h3>
                <p class="text-3xl font-bold text-purple-600">$15,420</p>
                <p class="text-sm text-gray-500">This month</p>
              </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
              <div class="space-y-2 text-sm">
                <p class="text-green-600">✅ Vue 3 + Router Working</p>
                <p class="text-green-600">✅ TailwindCSS Styling Working</p>
                <p class="text-green-600">✅ Component Navigation Working</p>
                <p class="text-green-600">✅ Pinia State Management Ready</p>
                <p class="text-blue-600">🔄 Ready for Authentication Integration</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'

const router = useRouter()
const authStore = useSimpleAuthStore()

const { user } = authStore

const logout = () => {
  authStore.logout()
  router.push('/')
}
</script>
