export type UserRole = 'client' | 'sales_manager' | 'general_manager' | 'ops_manager' | 'admin'

export interface UserProfile {
  uid: string
  email: string
  role: UserRole
  firstName: string
  lastName: string
  phone?: string
  businessName?: string
  address?: string
  city?: string
  country?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  lastLoginAt?: Date
  
  // Client-specific fields
  clientId?: string
  preferredProducts?: string[]
  tags?: string[]
  
  // Factory staff-specific fields
  department?: string
  permissions?: string[]
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  phone?: string
  businessName?: string
  role?: UserRole
}
