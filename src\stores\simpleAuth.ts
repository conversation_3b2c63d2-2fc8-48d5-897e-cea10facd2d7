import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface SimpleUser {
  id: string
  email: string
  name: string
  role: 'client' | 'sales_manager' | 'general_manager' | 'ops_manager' | 'admin'
  permissions?: string[]
}

export interface RolePermissions {
  [key: string]: string[]
}

export const ROLE_PERMISSIONS: RolePermissions = {
  client: [
    'orders:create',
    'orders:view_own',
    'orders:edit_own_pending'
  ],
  sales_manager: [
    'orders:view_all',
    'orders:approve',
    'orders:reject',
    'orders:edit',
    'orders:export',
    'clients:view',
    'metrics:sales'
  ],
  ops_manager: [
    'orders:view_all',
    'orders:view_production',
    'orders:start_production',
    'orders:complete_production',
    'production:manage',
    'equipment:monitor',
    'metrics:production'
  ],
  general_manager: [
    'orders:view_all',
    'orders:override',
    'production:view_all',
    'metrics:all',
    'reports:executive',
    'analytics:business'
  ],
  admin: [
    'system:full_access',
    'users:manage',
    'orders:force_approve',
    'orders:hard_delete',
    'system:diagnostics',
    'system:backup',
    'system:configuration',
    'metrics:system'
  ]
}

export const useSimpleAuthStore = defineStore('simpleAuth', () => {
  // State
  const user = ref<SimpleUser | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const userRole = computed(() => user.value?.role || null)
  const isClient = computed(() => userRole.value === 'client')
  const isFactoryStaff = computed(() =>
    ['sales_manager', 'general_manager', 'ops_manager', 'admin'].includes(userRole.value || '')
  )
  const userPermissions = computed(() => {
    if (!user.value?.role) return []
    return user.value.permissions || ROLE_PERMISSIONS[user.value.role] || []
  })

  // Permission checking methods
  const hasPermission = (permission: string): boolean => {
    if (!user.value) return false
    if (user.value.role === 'admin') return true // Admin has all permissions
    return userPermissions.value.includes(permission)
  }

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }

  const canEditOrders = computed(() => hasPermission('orders:edit'))
  const canApproveOrders = computed(() => hasPermission('orders:approve'))
  const canViewAllOrders = computed(() => hasPermission('orders:view_all'))
  const canManageProduction = computed(() => hasPermission('production:manage'))
  const canAccessSystemSettings = computed(() => hasPermission('system:full_access'))

  // Actions
  const login = async (email: string, password: string) => {
    loading.value = true
    error.value = null
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock user based on email
      const role = email.includes('admin') ? 'admin' :
                   email.includes('sales') ? 'sales_manager' :
                   email.includes('ops') ? 'ops_manager' :
                   email.includes('general') || email.includes('gm') ? 'general_manager' : 'client'

      const mockUser: SimpleUser = {
        id: Date.now().toString(),
        email,
        name: email.split('@')[0].replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        role,
        permissions: ROLE_PERMISSIONS[role]
      }
      
      user.value = mockUser
      return mockUser
    } catch (err) {
      error.value = 'Login failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    error.value = null
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user,
    loading,
    error,
    // Getters
    isAuthenticated,
    userRole,
    isClient,
    isFactoryStaff,
    userPermissions,
    // Permission methods
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canEditOrders,
    canApproveOrders,
    canViewAllOrders,
    canManageProduction,
    canAccessSystemSettings,
    // Actions
    login,
    logout,
    clearError
  }
})
