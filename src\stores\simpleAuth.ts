import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface SimpleUser {
  id: string
  email: string
  name: string
  role: 'client' | 'sales_manager' | 'general_manager' | 'ops_manager' | 'admin'
}

export const useSimpleAuthStore = defineStore('simpleAuth', () => {
  // State
  const user = ref<SimpleUser | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const userRole = computed(() => user.value?.role || null)
  const isClient = computed(() => userRole.value === 'client')
  const isFactoryStaff = computed(() => 
    ['sales_manager', 'general_manager', 'ops_manager', 'admin'].includes(userRole.value || '')
  )

  // Actions
  const login = async (email: string, password: string) => {
    loading.value = true
    error.value = null
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock user based on email
      const mockUser: SimpleUser = {
        id: Date.now().toString(),
        email,
        name: email.split('@')[0].replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        role: email.includes('admin') ? 'admin' :
              email.includes('sales') ? 'sales_manager' :
              email.includes('ops') ? 'ops_manager' :
              email.includes('general') || email.includes('gm') ? 'general_manager' : 'client'
      }
      
      user.value = mockUser
      return mockUser
    } catch (err) {
      error.value = 'Login failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    error.value = null
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user,
    loading,
    error,
    // Getters
    isAuthenticated,
    userRole,
    isClient,
    isFactoryStaff,
    // Actions
    login,
    logout,
    clearError
  }
})
