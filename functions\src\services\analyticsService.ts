import * as admin from 'firebase-admin';

const db = admin.firestore();

class AnalyticsService {
  async updateOrderStats(action: 'created' | 'approved' | 'rejected' | 'delivered' | 'cancelled') {
    const today = new Date();
    const dateKey = today.toISOString().split('T')[0]; // YYYY-MM-DD format
    
    const statsRef = db.collection('analytics').doc(`daily-${dateKey}`);
    
    await db.runTransaction(async (transaction) => {
      const doc = await transaction.get(statsRef);
      
      if (doc.exists) {
        const data = doc.data()!;
        transaction.update(statsRef, {
          [`orders.${action}`]: (data.orders?.[action] || 0) + 1,
          [`orders.total`]: (data.orders?.total || 0) + 1,
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
      } else {
        transaction.set(statsRef, {
          date: dateKey,
          orders: {
            total: 1,
            created: action === 'created' ? 1 : 0,
            approved: action === 'approved' ? 1 : 0,
            rejected: action === 'rejected' ? 1 : 0,
            delivered: action === 'delivered' ? 1 : 0,
            cancelled: action === 'cancelled' ? 1 : 0
          },
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
      }
    });
  }

  async generateDailyReport() {
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    
    // Get orders from yesterday
    const startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);
    
    const ordersSnapshot = await db.collection('orders')
      .where('createdAt', '>=', startOfDay)
      .where('createdAt', '<', endOfDay)
      .get();
    
    const orders = ordersSnapshot.docs.map(doc => doc.data());
    
    // Calculate metrics
    const metrics = {
      totalOrders: orders.length,
      approvedOrders: orders.filter(o => o.status === 'approved').length,
      rejectedOrders: orders.filter(o => o.status === 'rejected').length,
      pendingOrders: orders.filter(o => o.status === 'pending').length,
      totalQuantity: orders.reduce((sum, o) => sum + (o.totalQuantity || 0), 0),
      uniqueClients: new Set(orders.map(o => o.clientId)).size,
      averageOrderValue: orders.length > 0 ? orders.reduce((sum, o) => sum + (o.totalQuantity || 0), 0) / orders.length : 0
    };
    
    // Store daily report
    const dateKey = yesterday.toISOString().split('T')[0];
    await db.collection('analytics').doc(`daily-${dateKey}`).set({
      date: dateKey,
      type: 'daily_report',
      metrics,
      orders: {
        total: metrics.totalOrders,
        approved: metrics.approvedOrders,
        rejected: metrics.rejectedOrders,
        pending: metrics.pendingOrders
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    }, { merge: true });
    
    console.log(`Daily report generated for ${dateKey}:`, metrics);
  }

  async getAnalytics(startDate: Date, endDate: Date) {
    const analytics = await db.collection('analytics')
      .where('date', '>=', startDate.toISOString().split('T')[0])
      .where('date', '<=', endDate.toISOString().split('T')[0])
      .orderBy('date', 'desc')
      .get();
    
    return analytics.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }

  async getClientAnalytics(clientId: string, startDate: Date, endDate: Date) {
    const orders = await db.collection('orders')
      .where('clientId', '==', clientId)
      .where('createdAt', '>=', startDate)
      .where('createdAt', '<=', endDate)
      .get();
    
    const orderData = orders.docs.map(doc => doc.data());
    
    return {
      totalOrders: orderData.length,
      approvedOrders: orderData.filter(o => o.status === 'approved').length,
      rejectedOrders: orderData.filter(o => o.status === 'rejected').length,
      totalQuantity: orderData.reduce((sum, o) => sum + (o.totalQuantity || 0), 0),
      averageOrderSize: orderData.length > 0 ? orderData.reduce((sum, o) => sum + (o.totalQuantity || 0), 0) / orderData.length : 0,
      orders: orderData
    };
  }
}

export const analyticsService = new AnalyticsService();
