{"indexes": [{"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clientId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "assignedTo", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "orderId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "analytics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "ASCENDING"}]}], "fieldOverrides": []}