import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { orderTriggers } from './triggers/orderTriggers';
import { userTriggers } from './triggers/userTriggers';
import { analyticsService } from './services/analyticsService';
import { notificationService } from './services/notificationService';
import { googleSheetsService } from './services/googleSheetsService';
import { whatsappWebhook } from './webhooks/whatsappWebhook';

// Initialize Firebase Admin
admin.initializeApp();

// Export order-related triggers
export const onOrderCreated = orderTriggers.onOrderCreated;
export const onOrderUpdated = orderTriggers.onOrderUpdated;
export const onOrderStatusChanged = orderTriggers.onOrderStatusChanged;

// Export user-related triggers
export const onUserCreated = userTriggers.onUserCreated;
export const onUserUpdated = userTriggers.onUserUpdated;

// Export scheduled functions
export const generateDailyAnalytics = functions.pubsub
  .schedule('0 1 * * *') // Run daily at 1 AM
  .timeZone('Asia/Beirut')
  .onRun(async (context) => {
    await analyticsService.generateDailyReport();
    return null;
  });

export const syncToGoogleSheets = functions.pubsub
  .schedule('0 */6 * * *') // Run every 6 hours
  .timeZone('Asia/Beirut')
  .onRun(async (context) => {
    await googleSheetsService.syncApprovedOrders();
    return null;
  });

// Export HTTP functions
export const sendNotification = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { type, recipients, message, orderId } = data;
  await notificationService.sendNotification(type, recipients, message, orderId);
  
  return { success: true };
});

// WhatsApp webhook (for future integration)
export const whatsappWebhookHandler = functions.https.onRequest(whatsappWebhook);

// Utility function to clean up old logs
export const cleanupOldLogs = functions.pubsub
  .schedule('0 2 * * 0') // Run weekly on Sunday at 2 AM
  .timeZone('Asia/Beirut')
  .onRun(async (context) => {
    const db = admin.firestore();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 90); // Keep logs for 90 days

    const oldLogs = await db.collection('logs')
      .where('createdAt', '<', cutoffDate)
      .get();

    const batch = db.batch();
    oldLogs.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Cleaned up ${oldLogs.size} old log entries`);
    
    return null;
  });
