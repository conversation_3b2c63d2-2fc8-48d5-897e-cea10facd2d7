<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black">
    <!-- Ad<PERSON>er -->
    <nav class="bg-black/90 backdrop-blur-sm shadow-2xl border-b border-red-800 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-10 w-10 bg-gradient-to-r from-red-600 to-red-800 rounded-xl flex items-center justify-center shadow-lg">
              <span class="text-white font-bold text-lg">🛡️</span>
            </div>
            <div class="ml-3">
              <span class="text-xl font-bold text-red-400">Verto Admin</span>
              <div class="text-sm text-red-300 font-medium">System Control Center</div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <!-- System Health -->
            <div class="flex items-center space-x-2 bg-green-900/50 px-3 py-1 rounded-full border border-green-700">
              <div class="h-2 w-2 bg-green-400 rounded-full animate-pulse"></div>
              <span class="text-xs text-green-300 font-medium">{{ systemHealth.status }}</span>
            </div>
            <!-- Active Users -->
            <div class="flex items-center space-x-2 bg-blue-900/50 px-3 py-1 rounded-full border border-blue-700">
              <div class="h-2 w-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span class="text-xs text-blue-300 font-medium">{{ activeUsers.length }} Online</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-white">{{ user?.name }}</div>
              <div class="text-xs text-gray-400">System Administrator</div>
            </div>
            <button @click="logout" class="bg-red-600/20 hover:bg-red-600/30 text-red-300 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border border-red-700">
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">

      <!-- System Overview -->
      <div class="mb-8">
        <div class="bg-gradient-to-r from-red-600 to-red-800 rounded-2xl p-6 text-white shadow-2xl">
          <div class="flex justify-between items-center">
            <div>
              <h1 class="text-2xl font-bold mb-2">System Control Center 🛡️</h1>
              <p class="text-red-100">Monitor website activity, user behavior, and system health</p>
            </div>
            <div class="text-right">
              <div class="text-3xl font-bold">{{ systemHealth.uptime }}%</div>
              <div class="text-red-200 text-sm">System Uptime</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <button @click="generateBackup" class="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-gray-700 hover:shadow-xl transition-all hover:border-green-600 group">
          <div class="flex items-center">
            <div class="p-3 bg-green-900/50 rounded-lg border border-green-700 group-hover:bg-green-800/50">
              <span class="text-2xl">💾</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-300">Generate Backup</p>
              <p class="text-lg font-bold text-green-400">{{ backupRequests.length }} Pending</p>
            </div>
          </div>
        </button>

        <button @click="viewSystemLogs" class="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-gray-700 hover:shadow-xl transition-all hover:border-blue-600 group">
          <div class="flex items-center">
            <div class="p-3 bg-blue-900/50 rounded-lg border border-blue-700 group-hover:bg-blue-800/50">
              <span class="text-2xl">📋</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-300">System Logs</p>
              <p class="text-lg font-bold text-blue-400">{{ systemLogs.length }} Events</p>
            </div>
          </div>
        </button>

        <button @click="manageUsers" class="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-gray-700 hover:shadow-xl transition-all hover:border-purple-600 group">
          <div class="flex items-center">
            <div class="p-3 bg-purple-900/50 rounded-lg border border-purple-700 group-hover:bg-purple-800/50">
              <span class="text-2xl">👥</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-300">User Management</p>
              <p class="text-lg font-bold text-purple-400">{{ totalUsers }} Users</p>
            </div>
          </div>
        </button>

        <button @click="systemDiagnostics" class="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-gray-700 hover:shadow-xl transition-all hover:border-yellow-600 group">
          <div class="flex items-center">
            <div class="p-3 bg-yellow-900/50 rounded-lg border border-yellow-700 group-hover:bg-yellow-800/50">
              <span class="text-2xl">🔧</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-300">Diagnostics</p>
              <p class="text-lg font-bold text-yellow-400">{{ systemHealth.score }}/100</p>
            </div>
          </div>
        </button>
      </div>

      <!-- Live User Activity -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Active Users Panel -->
        <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
          <div class="bg-gradient-to-r from-blue-600 to-blue-800 px-6 py-4">
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">👥</span>
              Active Users ({{ activeUsers.length }})
            </h2>
            <p class="text-blue-100 text-sm mt-1">Real-time user activity monitoring</p>
          </div>

          <div class="p-6 max-h-96 overflow-y-auto">
            <div v-if="activeUsers.length === 0" class="text-center py-8 text-gray-400">
              <div class="text-4xl mb-2">👤</div>
              <p>No active users</p>
            </div>
            <div v-else class="space-y-3">
              <div v-for="user in activeUsers" :key="user.id"
                   class="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg border border-gray-600">
                <div class="flex items-center space-x-3">
                  <div :class="getUserStatusColor(user.role)" class="h-3 w-3 rounded-full animate-pulse"></div>
                  <div>
                    <div class="text-white font-medium">{{ user.name }}</div>
                    <div class="text-gray-400 text-sm">{{ user.role.replace('_', ' ').toUpperCase() }}</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-gray-300 text-sm">{{ user.currentPage }}</div>
                  <div class="text-gray-500 text-xs">{{ getTimeAgo(user.lastActivity) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Website Analytics Panel -->
        <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
          <div class="bg-gradient-to-r from-purple-600 to-purple-800 px-6 py-4">
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">📊</span>
              Website Analytics
            </h2>
            <p class="text-purple-100 text-sm mt-1">User behavior and engagement metrics</p>
          </div>

          <div class="p-6">
            <div class="space-y-6">
              <!-- Average Session Time -->
              <div>
                <div class="flex justify-between items-center mb-2">
                  <span class="text-gray-300 font-medium">Avg. Session Time</span>
                  <span class="text-white font-bold">{{ analytics.avgSessionTime }}</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                  <div class="bg-purple-500 h-2 rounded-full" style="width: 75%"></div>
                </div>
              </div>

              <!-- Page Views Today -->
              <div>
                <div class="flex justify-between items-center mb-2">
                  <span class="text-gray-300 font-medium">Page Views Today</span>
                  <span class="text-white font-bold">{{ analytics.pageViewsToday }}</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                  <div class="bg-blue-500 h-2 rounded-full" style="width: 60%"></div>
                </div>
              </div>

              <!-- User Retention -->
              <div>
                <div class="flex justify-between items-center mb-2">
                  <span class="text-gray-300 font-medium">User Retention</span>
                  <span class="text-white font-bold">{{ analytics.userRetention }}%</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                  <div class="bg-green-500 h-2 rounded-full" :style="{ width: analytics.userRetention + '%' }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Backup Requests & System Logs -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Backup Requests -->
        <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
          <div class="bg-gradient-to-r from-green-600 to-green-800 px-6 py-4">
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">💾</span>
              Backup Requests ({{ backupRequests.length }})
            </h2>
            <p class="text-green-100 text-sm mt-1">User-requested data backups</p>
          </div>

          <div class="p-6 max-h-96 overflow-y-auto">
            <div v-if="backupRequests.length === 0" class="text-center py-8 text-gray-400">
              <div class="text-4xl mb-2">💾</div>
              <p>No backup requests</p>
            </div>
            <div v-else class="space-y-3">
              <div v-for="request in backupRequests" :key="request.id"
                   class="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg border border-gray-600">
                <div class="flex items-center space-x-3">
                  <div class="h-3 w-3 bg-green-400 rounded-full animate-pulse"></div>
                  <div>
                    <div class="text-white font-medium">{{ request.requestedBy }}</div>
                    <div class="text-gray-400 text-sm">{{ request.type }} backup</div>
                  </div>
                </div>
                <div class="text-right">
                  <button @click="processBackup(request)"
                          class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-sm transition-colors">
                    Process
                  </button>
                  <div class="text-gray-500 text-xs mt-1">{{ getTimeAgo(request.requestedAt) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- System Logs -->
        <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
          <div class="bg-gradient-to-r from-orange-600 to-red-600 px-6 py-4">
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">📋</span>
              System Logs
            </h2>
            <p class="text-orange-100 text-sm mt-1">Recent system events and activities</p>
          </div>

          <div class="p-6 max-h-96 overflow-y-auto">
            <div class="space-y-2">
              <div v-for="log in systemLogs.slice(0, 10)" :key="log.id"
                   class="flex items-center justify-between p-2 bg-gray-700/30 rounded border border-gray-600">
                <div class="flex items-center space-x-3">
                  <div :class="getLogTypeColor(log.type)" class="h-2 w-2 rounded-full"></div>
                  <div>
                    <div class="text-white text-sm">{{ log.message }}</div>
                    <div class="text-gray-400 text-xs">{{ log.user }} • {{ getTimeAgo(log.timestamp) }}</div>
                  </div>
                </div>
                <div :class="getLogSeverityClass(log.severity)" class="px-2 py-1 rounded text-xs font-medium">
                  {{ log.severity }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSimpleAuthStore } from '@/stores/simpleAuth'
import { useOrderStore } from '@/stores/orderStore'
import { useRouter } from 'vue-router'

const authStore = useSimpleAuthStore()
const orderStore = useOrderStore()
const router = useRouter()

const user = computed(() => authStore.user)

// System Health Data
const systemHealth = ref({
  status: 'Online',
  uptime: 99.8,
  score: 95
})

// Active Users Data
const activeUsers = ref([
  {
    id: '1',
    name: 'John Client',
    role: 'client',
    currentPage: 'Dashboard',
    lastActivity: new Date(Date.now() - 5 * 60 * 1000) // 5 minutes ago
  },
  {
    id: '2',
    name: 'Sarah Sales',
    role: 'sales_manager',
    currentPage: 'Order Approval',
    lastActivity: new Date(Date.now() - 2 * 60 * 1000) // 2 minutes ago
  },
  {
    id: '3',
    name: 'Mike Operations',
    role: 'ops_manager',
    currentPage: 'Production Queue',
    lastActivity: new Date(Date.now() - 1 * 60 * 1000) // 1 minute ago
  }
])

// Website Analytics
const analytics = ref({
  avgSessionTime: '12m 34s',
  pageViewsToday: 247,
  bounceRate: 23,
  userRetention: 87
})

// Backup Requests
const backupRequests = ref([
  {
    id: '1',
    requestedBy: 'Sarah Sales',
    type: 'Orders',
    requestedAt: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
  },
  {
    id: '2',
    requestedBy: 'Mike Operations',
    type: 'Production Data',
    requestedAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
  }
])

// System Logs
const systemLogs = ref([
  {
    id: '1',
    type: 'auth',
    severity: 'INFO',
    message: 'User login successful',
    user: 'John Client',
    timestamp: new Date(Date.now() - 5 * 60 * 1000)
  },
  {
    id: '2',
    type: 'order',
    severity: 'INFO',
    message: 'New order created',
    user: 'Sarah Sales',
    timestamp: new Date(Date.now() - 10 * 60 * 1000)
  },
  {
    id: '3',
    type: 'system',
    severity: 'WARNING',
    message: 'High memory usage detected',
    user: 'System',
    timestamp: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: '4',
    type: 'production',
    severity: 'INFO',
    message: 'Order production completed',
    user: 'Mike Operations',
    timestamp: new Date(Date.now() - 20 * 60 * 1000)
  }
])

const totalUsers = computed(() => activeUsers.value.length + 15) // Mock total

// Methods
const getUserStatusColor = (role: string) => {
  const colors = {
    client: 'bg-blue-400',
    sales_manager: 'bg-green-400',
    ops_manager: 'bg-purple-400',
    admin: 'bg-red-400'
  }
  return colors[role as keyof typeof colors] || 'bg-gray-400'
}

const getLogTypeColor = (type: string) => {
  const colors = {
    auth: 'bg-blue-400',
    order: 'bg-green-400',
    system: 'bg-yellow-400',
    production: 'bg-purple-400'
  }
  return colors[type as keyof typeof colors] || 'bg-gray-400'
}

const getLogSeverityClass = (severity: string) => {
  const classes = {
    INFO: 'bg-blue-600 text-blue-100',
    WARNING: 'bg-yellow-600 text-yellow-100',
    ERROR: 'bg-red-600 text-red-100'
  }
  return classes[severity as keyof typeof classes] || 'bg-gray-600 text-gray-100'
}

const getTimeAgo = (date: Date) => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))

  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`

  const diffHours = Math.floor(diffMins / 60)
  if (diffHours < 24) return `${diffHours}h ago`

  const diffDays = Math.floor(diffHours / 24)
  return `${diffDays}d ago`
}

// Action Methods
const generateBackup = () => {
  alert('Backup generation started. You will be notified when complete.')
}

const viewSystemLogs = () => {
  alert('Opening detailed system logs...')
}

const manageUsers = () => {
  alert('Opening user management panel...')
}

const systemDiagnostics = () => {
  alert('Running system diagnostics...')
}

const processBackup = (request: any) => {
  const index = backupRequests.value.findIndex(r => r.id === request.id)
  if (index > -1) {
    backupRequests.value.splice(index, 1)
    alert(`Backup processed for ${request.requestedBy}`)
  }
}

const logout = () => {
  authStore.logout()
  router.push('/')
}

// Lifecycle
onMounted(() => {
  // Simulate real-time updates
  setInterval(() => {
    // Update active users activity
    activeUsers.value.forEach(user => {
      if (Math.random() > 0.7) {
        user.lastActivity = new Date()
      }
    })

    // Add new system logs occasionally
    if (Math.random() > 0.9) {
      systemLogs.value.unshift({
        id: Date.now().toString(),
        type: ['auth', 'order', 'system', 'production'][Math.floor(Math.random() * 4)],
        severity: ['INFO', 'WARNING'][Math.floor(Math.random() * 2)],
        message: 'System activity detected',
        user: 'System',
        timestamp: new Date()
      })

      // Keep only last 20 logs
      if (systemLogs.value.length > 20) {
        systemLogs.value = systemLogs.value.slice(0, 20)
      }
    }
  }, 5000) // Update every 5 seconds
})
</script>


