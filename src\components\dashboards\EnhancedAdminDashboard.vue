<template>
  <div class="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
    <!-- Enhanced Header -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-red-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-10 w-10 bg-gradient-to-r from-red-600 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
              <span class="text-white font-bold text-lg">⚙️</span>
            </div>
            <div class="ml-3">
              <span class="text-xl font-bold bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">Verto</span>
              <div class="text-sm text-red-600 font-medium">Developer Portal</div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <!-- System Status -->
            <div class="flex items-center space-x-2 bg-green-50 px-3 py-1 rounded-full border border-green-200">
              <div class="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-green-700 font-medium">System Online</span>
            </div>
            <!-- Performance Monitor -->
            <div class="flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-full">
              <div class="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-blue-700 font-medium">{{ systemMetrics.responseTime }}ms</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">{{ user?.name }}</div>
              <div class="text-xs text-gray-500">System Administrator</div>
            </div>
            <span class="bg-gradient-to-r from-red-100 to-orange-200 text-red-800 px-4 py-2 rounded-full font-medium text-sm shadow-sm">
              ⚙️ Admin
            </span>
            <button @click="logout" class="bg-red-50 hover:bg-red-100 text-red-600 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md">
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      
      <!-- Developer Welcome -->
      <div class="mb-8">
        <div class="bg-gradient-to-r from-red-600 to-orange-600 rounded-2xl p-6 text-white shadow-xl">
          <div class="flex justify-between items-center">
            <div>
              <h1 class="text-2xl font-bold mb-2">Developer Portal & System Control 🛠️</h1>
              <p class="text-red-100">Complete system administration, performance monitoring, and user management</p>
            </div>
            <div class="text-right">
              <div class="text-3xl font-bold">{{ systemMetrics.systemUptime }}%</div>
              <div class="text-red-200 text-sm">System Uptime</div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Health Dashboard -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-lg">
              <span class="text-2xl">👥</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Active Users</p>
              <p class="text-2xl font-bold text-green-600">{{ systemMetrics.activeUsers }}</p>
              <p class="text-xs text-green-500">Currently online</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg">
              <span class="text-2xl">⚡</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Response Time</p>
              <p class="text-2xl font-bold text-blue-600">{{ systemMetrics.responseTime }}ms</p>
              <p class="text-xs text-blue-500">Average latency</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-red-100 rounded-lg">
              <span class="text-2xl">🚨</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Error Rate</p>
              <p class="text-2xl font-bold text-red-600">{{ (systemMetrics.errorRate * 100).toFixed(2) }}%</p>
              <p class="text-xs text-red-500">System errors</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-purple-100 rounded-lg">
              <span class="text-2xl">🔒</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Data Integrity</p>
              <p class="text-2xl font-bold text-purple-600">{{ systemMetrics.dataIntegrity }}%</p>
              <p class="text-xs text-purple-500">Database health</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Admin Control Panels -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- User Management -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">👥</span>
              User Management
            </h2>
            <p class="text-blue-100 text-sm mt-1">Access control and user administration</p>
          </div>
          
          <div class="p-6">
            <div class="space-y-4">
              <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div>
                  <div class="font-medium text-blue-900">Client Users</div>
                  <div class="text-sm text-blue-600">Restaurant & Cafe accounts</div>
                </div>
                <span class="text-xl font-bold text-blue-600">{{ userStats.clients }}</span>
              </div>
              
              <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg border border-green-200">
                <div>
                  <div class="font-medium text-green-900">Factory Staff</div>
                  <div class="text-sm text-green-600">Internal team members</div>
                </div>
                <span class="text-xl font-bold text-green-600">{{ userStats.staff }}</span>
              </div>
              
              <div class="flex justify-between items-center p-3 bg-purple-50 rounded-lg border border-purple-200">
                <div>
                  <div class="font-medium text-purple-900">Administrators</div>
                  <div class="text-sm text-purple-600">System admins</div>
                </div>
                <span class="text-xl font-bold text-purple-600">{{ userStats.admins }}</span>
              </div>
            </div>
            
            <div class="mt-6 space-y-3">
              <button @click="manageUsers" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                👥 Manage Users
              </button>
              <button @click="createUser" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                ➕ Create New User
              </button>
              <button @click="auditLogs" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                📋 View Audit Logs
              </button>
            </div>
          </div>
        </div>

        <!-- System Performance -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div class="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4">
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">📊</span>
              Performance Monitor
            </h2>
            <p class="text-green-100 text-sm mt-1">Real-time system metrics</p>
          </div>
          
          <div class="p-6">
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-gray-600">CPU Usage</span>
                <span class="font-bold text-green-600">45%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-green-600 h-2 rounded-full" style="width: 45%"></div>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Memory Usage</span>
                <span class="font-bold text-blue-600">62%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: 62%"></div>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Database Load</span>
                <span class="font-bold text-purple-600">38%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-purple-600 h-2 rounded-full" style="width: 38%"></div>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Network I/O</span>
                <span class="font-bold text-orange-600">28%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-orange-600 h-2 rounded-full" style="width: 28%"></div>
              </div>
            </div>
            
            <div class="mt-6 space-y-3">
              <button @click="viewDetailedMetrics" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                📈 Detailed Metrics
              </button>
              <button @click="systemMaintenance" class="w-full bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                🔧 System Maintenance
              </button>
              <button @click="backupSystem" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                💾 Backup System
              </button>
            </div>
          </div>
        </div>

        <!-- Developer Tools -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div class="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-4">
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">🛠️</span>
              Developer Tools
            </h2>
            <p class="text-purple-100 text-sm mt-1">Development and debugging utilities</p>
          </div>
          
          <div class="p-6">
            <div class="space-y-4">
              <div class="p-3 bg-gray-50 rounded-lg">
                <div class="text-sm font-medium text-gray-900">API Status</div>
                <div class="text-xs text-green-600">✅ All endpoints operational</div>
              </div>
              
              <div class="p-3 bg-gray-50 rounded-lg">
                <div class="text-sm font-medium text-gray-900">Database Status</div>
                <div class="text-xs text-green-600">✅ Connected and healthy</div>
              </div>
              
              <div class="p-3 bg-gray-50 rounded-lg">
                <div class="text-sm font-medium text-gray-900">Cache Status</div>
                <div class="text-xs text-green-600">✅ Redis operational</div>
              </div>
              
              <div class="p-3 bg-gray-50 rounded-lg">
                <div class="text-sm font-medium text-gray-900">Queue Status</div>
                <div class="text-xs text-green-600">✅ Background jobs running</div>
              </div>
            </div>
            
            <div class="mt-6 space-y-3">
              <button @click="apiDocumentation" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                📚 API Documentation
              </button>
              <button @click="debugConsole" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                🐛 Debug Console
              </button>
              <button @click="deploymentTools" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                🚀 Deployment Tools
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Complete System Overview -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-gray-800 to-gray-900 px-6 py-4 flex justify-between items-center">
          <div>
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">🎛️</span>
              Complete System Administration
            </h2>
            <p class="text-gray-300 text-sm mt-1">Full order management with administrative controls</p>
          </div>
          <div class="flex space-x-3">
            <select v-model="statusFilter" class="bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 text-sm">
              <option value="">All Orders</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="in_production">In Production</option>
              <option value="completed">Completed</option>
              <option value="deleted">Deleted</option>
            </select>
            <button @click="exportSystemReport" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              📊 System Report
            </button>
          </div>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value & Timeline</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="order in filteredOrders" :key="order.id" 
                  class="hover:bg-gray-50 transition-colors"
                  :class="{ 'opacity-50 bg-red-50': order.status === 'deleted' }">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
                  <div class="text-sm text-gray-500">{{ formatDate(order.createdAt) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ order.clientName }}</div>
                  <div class="text-sm text-gray-500">{{ order.clientType }}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900">{{ order.potatoType }} - {{ order.cutShape }}</div>
                  <div class="text-sm text-gray-500">{{ order.bags }} bags ({{ order.bags * 25 }}kg)</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-bold text-green-600">${{ order.price }}</div>
                  <div class="text-sm text-gray-500">Due: {{ formatDate(order.deliveryDate) }}</div>
                  <div class="text-sm" :class="getUrgencyClass(order.deliveryDate)">{{ getTimeLeft(order.deliveryDate) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(order.status)" class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full">
                    {{ getStatusText(order.status) }}
                  </span>
                  <div v-if="order.flag" class="text-xs text-red-600 mt-1">🚩 {{ order.flag }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button @click="editOrder(order)" 
                          class="text-blue-600 hover:text-blue-900 font-medium">Edit</button>
                  <button @click="flagOrder(order)" 
                          class="text-orange-600 hover:text-orange-900 font-medium">Flag</button>
                  <button v-if="order.status !== 'deleted'" @click="deleteOrder(order)" 
                          class="text-red-600 hover:text-red-900 font-medium">Delete</button>
                  <button v-else @click="restoreOrder(order)" 
                          class="text-green-600 hover:text-green-900 font-medium">Restore</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'
import { useOrderStore } from '@/stores/orderStore'

const router = useRouter()
const authStore = useSimpleAuthStore()
const orderStore = useOrderStore()
const { user } = authStore

// State
const statusFilter = ref('')

// Computed properties
const systemMetrics = computed(() => orderStore.systemMetrics)

const userStats = computed(() => ({
  clients: 15,
  staff: 8,
  admins: 3
}))

const filteredOrders = computed(() => {
  let orders = orderStore.orders
  if (statusFilter.value) {
    orders = orders.filter(o => o.status === statusFilter.value)
  }
  return orders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
})

// Methods
const logout = () => {
  authStore.logout()
  router.push('/')
}

// User Management
const manageUsers = () => {
  alert(`👥 USER MANAGEMENT PANEL

Current Users:
• Client Users: ${userStats.value.clients}
• Factory Staff: ${userStats.value.staff}
• Administrators: ${userStats.value.admins}

Features:
✅ View all user accounts
✅ Edit user permissions
✅ Deactivate/activate accounts
✅ Reset passwords
✅ Audit user activity

This would open a comprehensive user management interface.`)
}

const createUser = () => {
  const userType = prompt('Select user type:\n1. Client (Restaurant/Cafe)\n2. Sales Manager\n3. Operations Manager\n4. General Manager\n5. Admin\n\nEnter number (1-5):')

  if (userType && ['1', '2', '3', '4', '5'].includes(userType)) {
    const types = ['client', 'sales_manager', 'ops_manager', 'general_manager', 'admin']
    const selectedType = types[parseInt(userType) - 1]

    alert(`✅ Creating new ${selectedType.replace('_', ' ')} account...

This would open a user creation form with:
• Basic information (name, email, phone)
• Role-specific permissions
• Initial password setup
• Account activation settings

User would be added to the system with appropriate access levels.`)
  }
}

const auditLogs = () => {
  alert(`📋 SYSTEM AUDIT LOGS

Recent Activity:
• 2024-01-15 14:30 - User login: <EMAIL>
• 2024-01-15 14:25 - Order #1234 approved by Sales Manager
• 2024-01-15 14:20 - New order created by client
• 2024-01-15 14:15 - Production completed for order #1230
• 2024-01-15 14:10 - System backup completed

Features:
✅ User activity tracking
✅ Order modification history
✅ System access logs
✅ Security event monitoring
✅ Export audit reports

This would show comprehensive system activity logs.`)
}

// Performance Monitoring
const viewDetailedMetrics = () => {
  alert(`📈 DETAILED SYSTEM METRICS

Performance Overview:
• CPU Usage: 45% (Normal)
• Memory Usage: 62% (Normal)
• Database Load: 38% (Low)
• Network I/O: 28% (Low)
• Response Time: ${systemMetrics.value.responseTime}ms
• Error Rate: ${(systemMetrics.value.errorRate * 100).toFixed(2)}%

Real-time Monitoring:
✅ Server health checks
✅ Database performance
✅ API endpoint monitoring
✅ User session tracking
✅ Resource utilization

This would open a comprehensive metrics dashboard.`)
}

const systemMaintenance = () => {
  alert(`🔧 SYSTEM MAINTENANCE PANEL

Available Operations:
• Clear application cache
• Optimize database indexes
• Clean temporary files
• Update system configurations
• Restart services
• Schedule maintenance windows

Current Status:
✅ All services running normally
✅ No maintenance required
✅ Next scheduled maintenance: Sunday 2:00 AM

This would provide system maintenance tools.`)
}

const backupSystem = () => {
  alert(`💾 SYSTEM BACKUP INITIATED

Backup Process:
✅ Database backup started
✅ File system backup in progress
✅ Configuration backup completed
✅ User data backup completed

Backup Details:
• Type: Full system backup
• Estimated time: 15 minutes
• Storage: Secure cloud storage
• Retention: 30 days
• Encryption: AES-256

Last backup: 2024-01-15 02:00 AM (Success)`)
}

// Developer Tools
const apiDocumentation = () => {
  alert(`📚 API DOCUMENTATION

Available Endpoints:
• POST /api/orders - Create new order
• GET /api/orders - List orders
• PUT /api/orders/:id - Update order
• DELETE /api/orders/:id - Delete order
• POST /api/auth/login - User authentication
• GET /api/metrics - System metrics

Authentication:
• JWT token required
• Role-based access control
• Rate limiting: 100 requests/minute

This would open interactive API documentation.`)
}

const debugConsole = () => {
  alert(`🐛 DEBUG CONSOLE

System Information:
• Environment: Production
• Version: 1.0.0
• Node.js: v18.17.0
• Database: PostgreSQL 15.3
• Cache: Redis 7.0.11

Debug Tools:
✅ Real-time log viewer
✅ Database query analyzer
✅ Performance profiler
✅ Error tracking
✅ Memory usage monitor

This would open a comprehensive debugging interface.`)
}

const deploymentTools = () => {
  alert(`🚀 DEPLOYMENT TOOLS

Current Deployment:
• Environment: Production
• Version: v1.0.0
• Last Deploy: 2024-01-15 10:00 AM
• Status: Healthy

Available Actions:
• Deploy new version
• Rollback to previous version
• View deployment history
• Configure environment variables
• Manage SSL certificates

This would provide deployment management tools.`)
}

// Order Management
const editOrder = (order: any) => {
  alert(`✏️ EDIT ORDER #${order.id}

Current Details:
• Client: ${order.clientName}
• Product: ${order.potatoType} - ${order.cutShape}
• Quantity: ${order.bags} bags
• Price: $${order.price}
• Status: ${order.status}

Admin can modify:
✅ Order details
✅ Pricing
✅ Delivery dates
✅ Status
✅ Priority level
✅ Special notes

This would open an order editing interface.`)
}

const flagOrder = (order: any) => {
  const reason = prompt('Flag this order for review. Enter reason:')
  if (reason) {
    alert(`🚩 Order #${order.id} has been flagged for review.

Reason: ${reason}

The order will be highlighted for management attention and may require special handling.`)
  }
}

const deleteOrder = async (order: any) => {
  if (confirm(`⚠️ Are you sure you want to delete order #${order.id}?\n\nThis will mark the order as deleted (soft delete) and it will be hidden from normal views but can be restored.`)) {
    try {
      await orderStore.deleteOrder(order.id)
      alert(`🗑️ Order #${order.id} has been deleted successfully.`)
    } catch (error) {
      alert('❌ Failed to delete order. Please try again.')
    }
  }
}

const restoreOrder = async (order: any) => {
  if (confirm(`♻️ Restore order #${order.id}?\n\nThis will make the order visible again and restore it to its previous status.`)) {
    try {
      await orderStore.restoreOrder(order.id)
      alert(`✅ Order #${order.id} has been restored successfully.`)
    } catch (error) {
      alert('❌ Failed to restore order. Please try again.')
    }
  }
}

const exportSystemReport = () => {
  const reportData = filteredOrders.value

  const csvContent = [
    ['SYSTEM ADMINISTRATION REPORT'],
    ['Generated:', new Date().toISOString()],
    ['Administrator:', user?.name || 'System Admin'],
    [''],
    ['SYSTEM METRICS'],
    ['Active Users', systemMetrics.value.activeUsers],
    ['Response Time', `${systemMetrics.value.responseTime}ms`],
    ['Error Rate', `${(systemMetrics.value.errorRate * 100).toFixed(2)}%`],
    ['System Uptime', `${systemMetrics.value.systemUptime}%`],
    ['Data Integrity', `${systemMetrics.value.dataIntegrity}%`],
    [''],
    ['ORDER DETAILS'],
    ['Order ID', 'Client', 'Product', 'Cut Shape', 'Bags', 'Price', 'Status', 'Created', 'Delivery Date'],
    ...reportData.map(order => [
      order.id,
      order.clientName,
      order.potatoType,
      order.cutShape,
      order.bags,
      order.price,
      order.status,
      order.createdAt,
      order.deliveryDate
    ])
  ].map(row => Array.isArray(row) ? row.join(',') : row).join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `system-admin-report-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
}

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    in_production: 'bg-blue-100 text-blue-800',
    completed: 'bg-purple-100 text-purple-800',
    delivered: 'bg-indigo-100 text-indigo-800',
    deleted: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '⏳ Pending',
    approved: '✅ Approved',
    rejected: '❌ Rejected',
    in_production: '🏭 Production',
    completed: '✅ Completed',
    delivered: '🚚 Delivered',
    deleted: '🗑️ Deleted'
  }
  return texts[status as keyof typeof texts] || status
}

const getTimeLeft = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'Overdue'
  if (days === 0) return 'Today'
  if (days === 1) return 'Tomorrow'
  return `${days} days`
}

const getUrgencyClass = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'text-red-600 font-bold'
  if (days <= 1) return 'text-orange-600 font-medium'
  if (days <= 3) return 'text-yellow-600'
  return 'text-gray-500'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
