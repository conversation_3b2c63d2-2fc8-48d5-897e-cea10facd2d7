<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-50 via-slate-50 to-zinc-50">
    <!-- Enhanced Header -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-gray-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-10 w-10 bg-gradient-to-r from-gray-800 to-black rounded-xl flex items-center justify-center shadow-lg">
              <span class="text-white font-bold text-lg">👔</span>
            </div>
            <div class="ml-3">
              <span class="text-xl font-bold bg-gradient-to-r from-gray-800 to-black bg-clip-text text-transparent">Verto</span>
              <div class="text-sm text-gray-600 font-medium">Executive Dashboard</div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <!-- System Health -->
            <div class="flex items-center space-x-2 bg-green-50 px-3 py-1 rounded-full border border-green-200">
              <div class="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-green-700 font-medium">System Healthy</span>
            </div>
            <!-- Live Updates -->
            <div class="flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-full">
              <div class="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-blue-700 font-medium">Live Analytics</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">{{ user?.name }}</div>
              <div class="text-xs text-gray-500">General Manager</div>
            </div>
            <span class="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 px-4 py-2 rounded-full font-medium text-sm shadow-sm">
              👔 Executive
            </span>
            <button @click="logout" class="bg-red-50 hover:bg-red-100 text-red-600 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md">
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      
      <!-- Executive Summary -->
      <div class="mb-8">
        <div class="bg-gradient-to-r from-gray-800 to-black rounded-2xl p-6 text-white shadow-xl">
          <div class="flex justify-between items-center">
            <div>
              <h1 class="text-2xl font-bold mb-2">Executive Overview 📊</h1>
              <p class="text-gray-300">Real-time factory performance and business intelligence</p>
            </div>
            <div class="text-right">
              <div class="text-3xl font-bold">${{ totalRevenue.toLocaleString() }}</div>
              <div class="text-gray-300 text-sm">Total Revenue</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Key Performance Indicators -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-lg">
              <span class="text-2xl">💰</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Revenue Today</p>
              <p class="text-2xl font-bold text-green-600">${{ dailyRevenue.toLocaleString() }}</p>
              <p class="text-xs text-green-500">+12% vs yesterday</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg">
              <span class="text-2xl">📦</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Orders Processed</p>
              <p class="text-2xl font-bold text-blue-600">{{ productionMetrics.completedToday }}</p>
              <p class="text-xs text-blue-500">{{ productionMetrics.totalBagsToday }} bags total</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-purple-100 rounded-lg">
              <span class="text-2xl">🏭</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Factory Efficiency</p>
              <p class="text-2xl font-bold text-purple-600">{{ productionMetrics.equipmentUtilization }}%</p>
              <p class="text-xs text-purple-500">Equipment utilization</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-yellow-100 rounded-lg">
              <span class="text-2xl">⭐</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Quality Score</p>
              <p class="text-2xl font-bold text-yellow-600">{{ productionMetrics.qualityScore.toFixed(1) }}%</p>
              <p class="text-xs text-yellow-500">Average rating</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Live Production Overview -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Production Status -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">🏭</span>
              Live Production Status
            </h2>
            <p class="text-blue-100 text-sm mt-1">Real-time factory operations</p>
          </div>
          
          <div class="p-6">
            <!-- Production Pipeline -->
            <div class="space-y-4">
              <div class="flex justify-between items-center p-4 bg-green-50 rounded-lg border border-green-200">
                <div class="flex items-center">
                  <div class="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
                  <span class="font-medium text-green-800">Ready for Production</span>
                </div>
                <span class="text-2xl font-bold text-green-600">{{ readyOrders.length }}</span>
              </div>
              
              <div class="flex justify-between items-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div class="flex items-center">
                  <div class="h-3 w-3 bg-blue-500 rounded-full mr-3 animate-pulse"></div>
                  <span class="font-medium text-blue-800">In Production</span>
                </div>
                <span class="text-2xl font-bold text-blue-600">{{ productionOrders.length }}</span>
              </div>
              
              <div class="flex justify-between items-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                <div class="flex items-center">
                  <div class="h-3 w-3 bg-purple-500 rounded-full mr-3"></div>
                  <span class="font-medium text-purple-800">Completed Today</span>
                </div>
                <span class="text-2xl font-bold text-purple-600">{{ completedToday.length }}</span>
              </div>
              
              <div class="flex justify-between items-center p-4 bg-orange-50 rounded-lg border border-orange-200">
                <div class="flex items-center">
                  <div class="h-3 w-3 bg-orange-500 rounded-full mr-3"></div>
                  <span class="font-medium text-orange-800">Pending Approval</span>
                </div>
                <span class="text-2xl font-bold text-orange-600">{{ pendingOrders.length }}</span>
              </div>
            </div>

            <!-- Production Timeline -->
            <div class="mt-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Today's Production Timeline</h3>
              <div class="space-y-2">
                <div v-for="order in recentCompletions" :key="order.id" 
                     class="flex justify-between items-center py-2 px-3 bg-gray-50 rounded-lg">
                  <div>
                    <span class="font-medium text-gray-900">#{{ order.id }}</span>
                    <span class="text-sm text-gray-500 ml-2">{{ order.bags }} bags</span>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-medium text-gray-900">{{ formatTime(order.updatedAt) }}</div>
                    <div class="text-xs text-green-600">✅ Completed</div>
                  </div>
                </div>
                
                <div v-if="recentCompletions.length === 0" class="text-center py-4 text-gray-500">
                  <div class="text-2xl mb-1">🏭</div>
                  <p class="text-sm">No completions yet today</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Business Analytics -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div class="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-4">
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">📈</span>
              Business Analytics
            </h2>
            <p class="text-purple-100 text-sm mt-1">Performance insights and trends</p>
          </div>
          
          <div class="p-6">
            <!-- Revenue Breakdown -->
            <div class="mb-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Revenue Breakdown</h3>
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <span class="text-gray-600">Completed Orders</span>
                  <span class="font-bold text-green-600">${{ completedRevenue.toLocaleString() }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-600">Pending Approval</span>
                  <span class="font-bold text-orange-600">${{ pendingRevenue.toLocaleString() }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-600">In Production</span>
                  <span class="font-bold text-blue-600">${{ productionRevenue.toLocaleString() }}</span>
                </div>
                <div class="border-t pt-2 mt-2">
                  <div class="flex justify-between items-center">
                    <span class="font-semibold text-gray-900">Total Pipeline</span>
                    <span class="font-bold text-gray-900">${{ totalRevenue.toLocaleString() }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Performance Metrics -->
            <div class="mb-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h3>
              <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                  <div class="text-2xl font-bold text-blue-600">{{ averageOrderSize.toFixed(0) }}</div>
                  <div class="text-xs text-blue-600">Avg Order Size (bags)</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                  <div class="text-2xl font-bold text-green-600">{{ productionMetrics.averageProductionTime.toFixed(1) }}h</div>
                  <div class="text-xs text-green-600">Avg Production Time</div>
                </div>
                <div class="text-center p-3 bg-purple-50 rounded-lg">
                  <div class="text-2xl font-bold text-purple-600">{{ approvalRate }}%</div>
                  <div class="text-xs text-purple-600">Approval Rate</div>
                </div>
                <div class="text-center p-3 bg-yellow-50 rounded-lg">
                  <div class="text-2xl font-bold text-yellow-600">{{ customerSatisfaction }}%</div>
                  <div class="text-xs text-yellow-600">Customer Satisfaction</div>
                </div>
              </div>
            </div>

            <!-- Top Clients -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Clients This Month</h3>
              <div class="space-y-2">
                <div v-for="client in topClients" :key="client.name" 
                     class="flex justify-between items-center py-2 px-3 bg-gray-50 rounded-lg">
                  <div>
                    <span class="font-medium text-gray-900">{{ client.name }}</span>
                    <span class="text-sm text-gray-500 ml-2">{{ client.orders }} orders</span>
                  </div>
                  <span class="font-bold text-green-600">${{ client.revenue.toLocaleString() }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Comprehensive Orders Overview -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-gray-800 to-gray-900 px-6 py-4 flex justify-between items-center">
          <div>
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">📊</span>
              Complete Orders Overview
            </h2>
            <p class="text-gray-300 text-sm mt-1">All orders with executive-level insights</p>
          </div>
          <div class="flex space-x-3">
            <select v-model="statusFilter" class="bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 text-sm">
              <option value="">All Status</option>
              <option value="pending">Pending Approval</option>
              <option value="approved">Ready for Production</option>
              <option value="in_production">In Production</option>
              <option value="completed">Completed</option>
            </select>
            <button @click="exportExecutiveReport" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              📊 Executive Report
            </button>
          </div>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value & Timeline</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="order in filteredOrders" :key="order.id" class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
                  <div class="text-sm text-gray-500">{{ formatDate(order.createdAt) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ order.clientName }}</div>
                  <div class="text-sm text-gray-500">{{ order.clientType }}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900">{{ order.potatoType }} - {{ order.cutShape }}</div>
                  <div class="text-sm text-gray-500">{{ order.bags }} bags ({{ order.bags * 25 }}kg)</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-bold text-green-600">${{ order.price }}</div>
                  <div class="text-sm text-gray-500">Due: {{ formatDate(order.deliveryDate) }}</div>
                  <div class="text-sm" :class="getUrgencyClass(order.deliveryDate)">{{ getTimeLeft(order.deliveryDate) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div v-if="order.actualProductionTime" class="text-sm text-gray-900">
                    {{ order.actualProductionTime.toFixed(1) }}h / {{ order.estimatedProductionTime }}h
                  </div>
                  <div v-else class="text-sm text-gray-500">Est: {{ order.estimatedProductionTime }}h</div>
                  <div v-if="order.qualityScore" class="text-sm text-green-600">
                    Quality: {{ order.qualityScore }}%
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(order.status)" class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full">
                    {{ getStatusText(order.status) }}
                  </span>
                  <div v-if="order.productionStatus" class="text-xs text-gray-500 mt-1">
                    {{ order.productionStatus }}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'
import { useOrderStore } from '@/stores/orderStore'

const router = useRouter()
const authStore = useSimpleAuthStore()
const orderStore = useOrderStore()
const { user } = authStore

// State
const statusFilter = ref('')

// Computed properties
const productionMetrics = computed(() => orderStore.productionMetrics)
const pendingOrders = computed(() => orderStore.pendingOrders)
const productionOrders = computed(() => orderStore.productionOrders)

const readyOrders = computed(() =>
  orderStore.orders.filter(o => o.status === 'approved')
)

const completedToday = computed(() => {
  const today = new Date().toDateString()
  return orderStore.orders.filter(o =>
    o.status === 'completed' &&
    new Date(o.updatedAt).toDateString() === today
  )
})

const recentCompletions = computed(() =>
  completedToday.value
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 5)
)

const totalRevenue = computed(() =>
  orderStore.orders.reduce((sum, order) => sum + order.price, 0)
)

const dailyRevenue = computed(() => {
  const today = new Date().toDateString()
  return orderStore.orders
    .filter(o => new Date(o.createdAt).toDateString() === today)
    .reduce((sum, order) => sum + order.price, 0)
})

const completedRevenue = computed(() =>
  orderStore.orders
    .filter(o => o.status === 'completed')
    .reduce((sum, order) => sum + order.price, 0)
)

const pendingRevenue = computed(() =>
  pendingOrders.value.reduce((sum, order) => sum + order.price, 0)
)

const productionRevenue = computed(() =>
  productionOrders.value.reduce((sum, order) => sum + order.price, 0)
)

const averageOrderSize = computed(() => {
  const orders = orderStore.orders
  return orders.length > 0 ? orders.reduce((sum, o) => sum + o.bags, 0) / orders.length : 0
})

const approvalRate = computed(() => {
  const thisMonth = new Date().getMonth()
  const monthlyOrders = orderStore.orders.filter(o =>
    new Date(o.createdAt).getMonth() === thisMonth
  )
  const approved = monthlyOrders.filter(o => o.status === 'approved' || o.status === 'completed').length
  return monthlyOrders.length > 0 ? Math.round((approved / monthlyOrders.length) * 100) : 0
})

const customerSatisfaction = computed(() => {
  const completedWithQuality = orderStore.orders.filter(o => o.status === 'completed' && o.qualityScore)
  if (completedWithQuality.length === 0) return 95

  const avgQuality = completedWithQuality.reduce((sum, o) => sum + (o.qualityScore || 0), 0) / completedWithQuality.length
  return Math.round(avgQuality)
})

const topClients = computed(() => {
  const thisMonth = new Date().getMonth()
  const clientStats = new Map()

  orderStore.orders
    .filter(o => new Date(o.createdAt).getMonth() === thisMonth)
    .forEach(order => {
      const existing = clientStats.get(order.clientName) || { name: order.clientName, orders: 0, revenue: 0 }
      existing.orders += 1
      existing.revenue += order.price
      clientStats.set(order.clientName, existing)
    })

  return Array.from(clientStats.values())
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5)
})

const filteredOrders = computed(() => {
  let orders = orderStore.orders
  if (statusFilter.value) {
    orders = orders.filter(o => o.status === statusFilter.value)
  }
  return orders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
})

// Methods
const logout = () => {
  authStore.logout()
  router.push('/')
}

const exportExecutiveReport = () => {
  const reportData = {
    summary: {
      totalOrders: orderStore.orders.length,
      totalRevenue: totalRevenue.value,
      dailyRevenue: dailyRevenue.value,
      approvalRate: approvalRate.value,
      customerSatisfaction: customerSatisfaction.value,
      averageOrderSize: averageOrderSize.value
    },
    production: productionMetrics.value,
    topClients: topClients.value
  }

  const csvContent = [
    ['EXECUTIVE SUMMARY REPORT'],
    ['Generated:', new Date().toISOString()],
    [''],
    ['KEY METRICS'],
    ['Total Orders', orderStore.orders.length],
    ['Total Revenue', `$${totalRevenue.value}`],
    ['Daily Revenue', `$${dailyRevenue.value}`],
    ['Approval Rate', `${approvalRate.value}%`],
    ['Customer Satisfaction', `${customerSatisfaction.value}%`],
    ['Average Order Size', `${averageOrderSize.value.toFixed(1)} bags`],
    [''],
    ['PRODUCTION METRICS'],
    ['Equipment Utilization', `${productionMetrics.value.equipmentUtilization}%`],
    ['Quality Score', `${productionMetrics.value.qualityScore.toFixed(1)}%`],
    ['Average Production Time', `${productionMetrics.value.averageProductionTime.toFixed(1)}h`],
    [''],
    ['TOP CLIENTS'],
    ['Client Name', 'Orders', 'Revenue'],
    ...topClients.value.map(client => [client.name, client.orders, `$${client.revenue}`])
  ].map(row => Array.isArray(row) ? row.join(',') : row).join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `executive-report-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
}

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    in_production: 'bg-blue-100 text-blue-800',
    completed: 'bg-purple-100 text-purple-800',
    delivered: 'bg-indigo-100 text-indigo-800',
    deleted: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '⏳ Pending',
    approved: '✅ Approved',
    rejected: '❌ Rejected',
    in_production: '🏭 Production',
    completed: '✅ Completed',
    delivered: '🚚 Delivered',
    deleted: '🗑️ Cancelled'
  }
  return texts[status as keyof typeof texts] || status
}

const getTimeLeft = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'Overdue'
  if (days === 0) return 'Today'
  if (days === 1) return 'Tomorrow'
  return `${days} days`
}

const getUrgencyClass = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'text-red-600 font-bold'
  if (days <= 1) return 'text-orange-600 font-medium'
  if (days <= 3) return 'text-yellow-600'
  return 'text-gray-500'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatTime = (date: string) => {
  return new Date(date).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
