import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { notificationService } from '../services/notificationService';
import { auditService } from '../services/auditService';

const db = admin.firestore();

export const userTriggers = {
  onUserCreated: functions.firestore
    .document('users/{userId}')
    .onCreate(async (snap, context) => {
      const user = snap.data();
      const userId = context.params.userId;

      try {
        // Send welcome email
        await notificationService.sendNotification(
          'user_created',
          [user.email],
          `Welcome to Verto Order System! Your account has been created successfully.`,
          undefined,
          { role: user.role, firstName: user.firstName }
        );

        // Log user creation
        await auditService.logAction({
          action: 'user_created',
          userId: userId,
          details: {
            email: user.email,
            role: user.role,
            firstName: user.firstName,
            lastName: user.lastName
          }
        });

        console.log(`User ${userId} created successfully`);
      } catch (error) {
        console.error('Error processing user creation:', error);
      }
    }),

  onUserUpdated: functions.firestore
    .document('users/{userId}')
    .onUpdate(async (change, context) => {
      const before = change.before.data();
      const after = change.after.data();
      const userId = context.params.userId;

      try {
        // Check if role changed
        if (before.role !== after.role) {
          await auditService.logAction({
            action: 'user_role_changed',
            userId: userId,
            details: {
              oldRole: before.role,
              newRole: after.role,
              changedBy: after.updatedBy || 'system'
            }
          });
        }

        // Check if account was activated/deactivated
        if (before.isActive !== after.isActive) {
          await auditService.logAction({
            action: after.isActive ? 'user_activated' : 'user_deactivated',
            userId: userId,
            details: {
              changedBy: after.updatedBy || 'system'
            }
          });
        }

        console.log(`User ${userId} updated successfully`);
      } catch (error) {
        console.error('Error processing user update:', error);
      }
    })
};
