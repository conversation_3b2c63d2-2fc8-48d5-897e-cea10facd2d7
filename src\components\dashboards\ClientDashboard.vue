<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-green-600 rounded-md flex items-center justify-center">
              <span class="text-white font-bold text-sm">V</span>
            </div>
            <span class="ml-2 text-xl font-semibold text-gray-900">Verto</span>
            <span class="ml-2 text-sm text-gray-500">Client Portal</span>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-gray-700">{{ user?.name }}</span>
            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Client</span>
            <button @click="logout" class="text-blue-600 hover:text-blue-800">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">My Orders</h3>
            <p class="text-3xl font-bold text-blue-600">{{ orders.length }}</p>
            <p class="text-sm text-gray-500">Total orders placed</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Pending</h3>
            <p class="text-3xl font-bold text-orange-600">{{ pendingOrders }}</p>
            <p class="text-sm text-gray-500">Awaiting approval</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">This Month</h3>
            <p class="text-3xl font-bold text-green-600">{{ thisMonthOrders }}</p>
            <p class="text-sm text-gray-500">Orders this month</p>
          </div>
        </div>

        <!-- New Order Form -->
        <div class="bg-white rounded-lg shadow mb-8">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">🥔 Place New Order</h2>
          </div>
          <div class="p-6">
            <form @submit.prevent="submitOrder" class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Potato Type</label>
                <select v-model="newOrder.potatoType" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                  <option value="">Select potato type</option>
                  <option value="russet">Russet Potatoes</option>
                  <option value="red">Red Potatoes</option>
                  <option value="yukon">Yukon Gold</option>
                  <option value="fingerling">Fingerling</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Cut Shape</label>
                <select v-model="newOrder.cutShape" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                  <option value="">Select cut shape</option>
                  <option value="cubes">Cubes (1cm x 1cm)</option>
                  <option value="long">Long Strips (French Fry Style)</option>
                  <option value="short">Short Strips</option>
                  <option value="wedges">Wedges</option>
                  <option value="whole">Whole (Uncut)</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Number of Bags</label>
                <input v-model.number="newOrder.bags" type="number" min="1" max="1000" required 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                       placeholder="Enter number of bags">
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Delivery Date</label>
                <input v-model="newOrder.deliveryDate" type="date" required :min="minDate"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
              </div>

              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Special Instructions (Optional)</label>
                <textarea v-model="newOrder.notes" rows="3" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                          placeholder="Any special requirements or notes..."></textarea>
              </div>

              <div class="md:col-span-2">
                <button type="submit" 
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                  🚀 Submit Order
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Orders History -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">📋 My Orders</h2>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type & Cut</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bags</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="order in orders" :key="order.id" :class="{ 'opacity-50': order.status === 'deleted' }">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ order.id }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ order.potatoType }} - {{ order.cutShape }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.bags }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(order.deliveryDate) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getStatusClass(order.status)" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                      {{ order.status }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button v-if="order.status === 'pending'" @click="editOrder(order)" 
                            class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                    <button @click="viewOrder(order)" class="text-green-600 hover:text-green-900">View</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'

const router = useRouter()
const authStore = useSimpleAuthStore()
const { user } = authStore

// Order data
const orders = ref([
  {
    id: '001',
    potatoType: 'russet',
    cutShape: 'cubes',
    bags: 50,
    deliveryDate: '2024-01-15',
    status: 'approved',
    notes: 'For weekend rush',
    createdAt: '2024-01-10'
  },
  {
    id: '002',
    potatoType: 'red',
    cutShape: 'wedges',
    bags: 25,
    deliveryDate: '2024-01-20',
    status: 'pending',
    notes: '',
    createdAt: '2024-01-12'
  }
])

// New order form
const newOrder = ref({
  potatoType: '',
  cutShape: '',
  bags: 1,
  deliveryDate: '',
  notes: ''
})

// Computed properties
const pendingOrders = computed(() => orders.value.filter(o => o.status === 'pending').length)
const thisMonthOrders = computed(() => orders.value.filter(o => 
  new Date(o.createdAt).getMonth() === new Date().getMonth()
).length)

const minDate = computed(() => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().split('T')[0]
})

// Methods
const submitOrder = () => {
  const order = {
    id: (orders.value.length + 1).toString().padStart(3, '0'),
    ...newOrder.value,
    status: 'pending',
    createdAt: new Date().toISOString().split('T')[0]
  }
  
  orders.value.unshift(order)
  
  // Reset form
  newOrder.value = {
    potatoType: '',
    cutShape: '',
    bags: 1,
    deliveryDate: '',
    notes: ''
  }
  
  alert('Order submitted successfully! 🎉')
}

const editOrder = (order: any) => {
  alert(`Edit order #${order.id} - Feature coming soon!`)
}

const viewOrder = (order: any) => {
  alert(`Order #${order.id} Details:\n${order.potatoType} - ${order.cutShape}\n${order.bags} bags\nDelivery: ${order.deliveryDate}`)
}

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    completed: 'bg-blue-100 text-blue-800',
    deleted: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}

const logout = () => {
  authStore.logout()
  router.push('/')
}

onMounted(() => {
  // Set default delivery date to tomorrow
  newOrder.value.deliveryDate = minDate.value
})
</script>
