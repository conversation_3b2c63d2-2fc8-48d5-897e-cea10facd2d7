import * as admin from 'firebase-admin';
import * as nodemailer from 'nodemailer';
import * as functions from 'firebase-functions';

const db = admin.firestore();

interface NotificationData {
  type: string;
  recipients: string[];
  message: string;
  orderId?: string;
  metadata?: any;
}

class NotificationService {
  private emailTransporter: nodemailer.Transporter;

  constructor() {
    // Initialize email transporter (configure with your email service)
    this.emailTransporter = nodemailer.createTransporter({
      service: 'gmail', // or your preferred email service
      auth: {
        user: functions.config().email?.user || process.env.EMAIL_USER,
        pass: functions.config().email?.password || process.env.EMAIL_PASSWORD
      }
    });
  }

  async sendNotification(type: string, recipients: string[], message: string, orderId?: string, metadata?: any) {
    try {
      // Log notification
      await this.logNotification({
        type,
        recipients,
        message,
        orderId,
        metadata
      });

      // Send email notifications
      await this.sendEmailNotifications(type, recipients, message, orderId, metadata);

      // Send in-app notifications
      await this.sendInAppNotifications(type, recipients, message, orderId, metadata);

      console.log(`Notification sent successfully: ${type} to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }

  private async sendEmailNotifications(type: string, recipients: string[], message: string, orderId?: string, metadata?: any) {
    const subject = this.getEmailSubject(type, orderId);
    const htmlContent = this.getEmailTemplate(type, message, orderId, metadata);

    for (const recipient of recipients) {
      try {
        await this.emailTransporter.sendMail({
          from: `"Verto Order System" <${functions.config().email?.user || process.env.EMAIL_USER}>`,
          to: recipient,
          subject: subject,
          html: htmlContent
        });
      } catch (error) {
        console.error(`Failed to send email to ${recipient}:`, error);
      }
    }
  }

  private async sendInAppNotifications(type: string, recipients: string[], message: string, orderId?: string, metadata?: any) {
    // Get user IDs from email addresses
    const userPromises = recipients.map(email => 
      db.collection('users').where('email', '==', email).limit(1).get()
    );
    
    const userSnapshots = await Promise.all(userPromises);
    
    for (const snapshot of userSnapshots) {
      if (!snapshot.empty) {
        const userId = snapshot.docs[0].id;
        
        await db.collection('notifications').add({
          userId,
          type,
          message,
          orderId: orderId || null,
          metadata: metadata || null,
          read: false,
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        });
      }
    }
  }

  private getEmailSubject(type: string, orderId?: string): string {
    const orderRef = orderId ? ` - Order #${orderId}` : '';
    
    switch (type) {
      case 'new_order':
        return `New Order Received${orderRef}`;
      case 'order_approved':
        return `Order Approved${orderRef}`;
      case 'order_rejected':
        return `Order Rejected${orderRef}`;
      case 'order_delivered':
        return `Order Delivered${orderRef}`;
      case 'order_cancelled':
        return `Order Cancelled${orderRef}`;
      case 'user_created':
        return 'Welcome to Verto Order System';
      case 'password_reset':
        return 'Password Reset Request';
      default:
        return `Verto Order System Notification${orderRef}`;
    }
  }

  private getEmailTemplate(type: string, message: string, orderId?: string, metadata?: any): string {
    const baseTemplate = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verto Order System</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          .button { display: inline-block; padding: 10px 20px; background-color: #2563eb; color: white; text-decoration: none; border-radius: 5px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Verto Order System</h1>
          </div>
          <div class="content">
            <p>${message}</p>
            ${orderId ? `<p><strong>Order ID:</strong> ${orderId}</p>` : ''}
            ${this.getTypeSpecificContent(type, metadata)}
            <p><a href="${this.getAppUrl()}" class="button">View in Dashboard</a></p>
          </div>
          <div class="footer">
            <p>© 2025 Verto Order System. All rights reserved.</p>
            <p>Kfarnabrakh, Lebanon</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return baseTemplate;
  }

  private getTypeSpecificContent(type: string, metadata?: any): string {
    switch (type) {
      case 'new_order':
        if (metadata) {
          return `
            <p><strong>Client:</strong> ${metadata.clientName}</p>
            <p><strong>Items:</strong> ${metadata.itemCount}</p>
            <p><strong>Total Quantity:</strong> ${metadata.totalQuantity}</p>
          `;
        }
        break;
      case 'order_rejected':
        if (metadata?.rejectionReason) {
          return `<p><strong>Rejection Reason:</strong> ${metadata.rejectionReason}</p>`;
        }
        break;
    }
    return '';
  }

  private getAppUrl(): string {
    // Return your app's URL
    return functions.config().app?.url || process.env.APP_URL || 'https://verto-order-system.web.app';
  }

  private async logNotification(data: NotificationData) {
    await db.collection('logs').add({
      action: 'notification_sent',
      type: data.type,
      recipients: data.recipients,
      orderId: data.orderId || null,
      metadata: data.metadata || null,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    });
  }

  // Method to mark in-app notifications as read
  async markNotificationAsRead(notificationId: string, userId: string) {
    const notificationRef = db.collection('notifications').doc(notificationId);
    const notification = await notificationRef.get();
    
    if (notification.exists && notification.data()?.userId === userId) {
      await notificationRef.update({
        read: true,
        readAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  }

  // Method to get unread notifications for a user
  async getUnreadNotifications(userId: string) {
    const notifications = await db.collection('notifications')
      .where('userId', '==', userId)
      .where('read', '==', false)
      .orderBy('createdAt', 'desc')
      .limit(50)
      .get();
    
    return notifications.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }
}

export const notificationService = new NotificationService();
