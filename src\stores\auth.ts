import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from 'firebase/auth'
import { authService } from '@/services/auth'
import type { UserRole, UserProfile } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const userProfile = ref<UserProfile | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const userRole = computed(() => userProfile.value?.role || null)
  const isClient = computed(() => userRole.value === 'client')
  const isFactoryStaff = computed(() => 
    ['sales_manager', 'general_manager', 'ops_manager', 'admin'].includes(userRole.value || '')
  )
  const isSalesManager = computed(() => userRole.value === 'sales_manager')
  const isGeneralManager = computed(() => userRole.value === 'general_manager')
  const isOpsManager = computed(() => userRole.value === 'ops_manager')
  const isAdmin = computed(() => userRole.value === 'admin')

  // Actions
  const setUser = (newUser: User | null) => {
    user.value = newUser
  }

  const setUserProfile = (profile: UserProfile | null) => {
    userProfile.value = profile
  }

  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const hasRole = (role: string): boolean => {
    if (!userProfile.value) return false
    
    // Admin has access to everything
    if (userProfile.value.role === 'admin') return true
    
    // Check specific role
    if (userProfile.value.role === role) return true
    
    // Factory staff roles
    if (role === 'factory' && isFactoryStaff.value) return true
    
    return false
  }

  const login = async (email: string, password: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await authService.signIn(email, password)
      setUser(result.user)
      
      // Fetch user profile
      const profile = await authService.getUserProfile(result.user.uid)
      setUserProfile(profile)
      
      return result
    } catch (err: any) {
      setError(err.message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const register = async (email: string, password: string, userData: Partial<UserProfile>) => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await authService.signUp(email, password, userData)
      setUser(result.user)
      
      // Fetch user profile
      const profile = await authService.getUserProfile(result.user.uid)
      setUserProfile(profile)
      
      return result
    } catch (err: any) {
      setError(err.message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      setLoading(true)
      await authService.signOut()
      setUser(null)
      setUserProfile(null)
      setError(null)
    } catch (err: any) {
      setError(err.message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const initializeAuth = async () => {
    try {
      setLoading(true)
      const currentUser = await authService.getCurrentUser()
      
      if (currentUser) {
        setUser(currentUser)
        const profile = await authService.getUserProfile(currentUser.uid)
        setUserProfile(profile)
      }
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return {
    // State
    user,
    userProfile,
    loading,
    error,
    
    // Getters
    isAuthenticated,
    userRole,
    isClient,
    isFactoryStaff,
    isSalesManager,
    isGeneralManager,
    isOpsManager,
    isAdmin,
    
    // Actions
    setUser,
    setUserProfile,
    setLoading,
    setError,
    hasRole,
    login,
    register,
    logout,
    initializeAuth
  }
})
