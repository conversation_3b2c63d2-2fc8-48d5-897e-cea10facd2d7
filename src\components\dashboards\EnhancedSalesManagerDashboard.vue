<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <!-- Enhanced Header -->
    <nav class="bg-white/90 backdrop-blur-sm shadow-lg border-b border-blue-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-10 w-10 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg">
              <span class="text-white font-bold text-lg">📊</span>
            </div>
            <div class="ml-3">
              <span class="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Verto</span>
              <div class="text-sm text-blue-600 font-medium">Sales Management</div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <!-- Pending Orders Alert -->
            <div v-if="pendingOrders.length > 0" class="flex items-center space-x-2 bg-orange-50 px-3 py-1 rounded-full border border-orange-200">
              <div class="h-2 w-2 bg-orange-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-orange-700 font-medium">{{ pendingOrders.length }} Pending</span>
            </div>
            <!-- Real-time indicator -->
            <div class="flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-full">
              <div class="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-blue-700 font-medium">Live</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">{{ user?.name }}</div>
              <div class="text-xs text-gray-500">Sales Manager</div>
            </div>
            <span class="bg-gradient-to-r from-blue-100 to-indigo-200 text-blue-800 px-4 py-2 rounded-full font-medium text-sm shadow-sm">
              📊 Sales Manager
            </span>
            <button @click="logout" class="bg-red-50 hover:bg-red-100 text-red-600 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md">
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      
      <!-- Welcome & Quick Actions -->
      <div class="mb-8">
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-6 text-white shadow-xl">
          <div class="flex justify-between items-center">
            <div>
              <h1 class="text-2xl font-bold mb-2">Sales Dashboard 📊</h1>
              <p class="text-blue-100">Manage orders, approve requests, and drive sales growth</p>
            </div>
            <div class="text-right">
              <div class="text-3xl font-bold">{{ pendingOrders.length }}</div>
              <div class="text-blue-200 text-sm">Orders to Review</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-orange-100 rounded-lg">
              <span class="text-2xl">⏳</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Pending Approval</p>
              <p class="text-2xl font-bold text-orange-600">{{ pendingOrders.length }}</p>
              <p class="text-xs text-gray-500">Requires action</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-lg">
              <span class="text-2xl">✅</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Approved Today</p>
              <p class="text-2xl font-bold text-green-600">{{ approvedToday }}</p>
              <p class="text-xs text-gray-500">Orders processed</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-purple-100 rounded-lg">
              <span class="text-2xl">💰</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Revenue Impact</p>
              <p class="text-2xl font-bold text-purple-600">${{ pendingRevenue.toLocaleString() }}</p>
              <p class="text-xs text-gray-500">Pending approval</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg">
              <span class="text-2xl">🎯</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Approval Rate</p>
              <p class="text-2xl font-bold text-blue-600">{{ approvalRate }}%</p>
              <p class="text-xs text-gray-500">This month</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Priority Orders Section -->
      <div v-if="pendingOrders.length > 0" class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-8">
        <div class="bg-gradient-to-r from-orange-500 to-red-500 px-6 py-4">
          <h2 class="text-xl font-bold text-white flex items-center">
            <span class="text-2xl mr-2">🚨</span>
            Priority Orders - Immediate Action Required
          </h2>
          <p class="text-orange-100 text-sm mt-1">{{ pendingOrders.length }} orders waiting for your approval</p>
        </div>
        
        <div class="p-6">
          <div class="grid gap-4">
            <div v-for="order in pendingOrders" :key="order.id" 
                 class="border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-all duration-200"
                 :class="getPriorityBorderClass(order)">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="flex items-center space-x-3 mb-3">
                    <span :class="getPriorityClass(order)" class="px-3 py-1 text-xs font-bold rounded-full">
                      {{ getPriority(order) }}
                    </span>
                    <div class="text-lg font-bold text-gray-900">#{{ order.id }}</div>
                    <div class="text-sm text-gray-500">{{ order.clientName }}</div>
                  </div>
                  
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <div class="text-xs text-gray-500 uppercase tracking-wide">Product</div>
                      <div class="font-medium">{{ order.potatoType }} - {{ order.cutShape }}</div>
                    </div>
                    <div>
                      <div class="text-xs text-gray-500 uppercase tracking-wide">Quantity</div>
                      <div class="font-medium">{{ order.bags }} bags</div>
                    </div>
                    <div>
                      <div class="text-xs text-gray-500 uppercase tracking-wide">Delivery</div>
                      <div class="font-medium" :class="getUrgencyClass(order.deliveryDate)">
                        {{ formatDate(order.deliveryDate) }}
                      </div>
                    </div>
                    <div>
                      <div class="text-xs text-gray-500 uppercase tracking-wide">Value</div>
                      <div class="font-medium text-green-600">${{ order.price }}</div>
                    </div>
                  </div>
                  
                  <div v-if="order.notes" class="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg mb-4">
                    <strong>Notes:</strong> {{ order.notes }}
                  </div>
                </div>
              </div>
              
              <!-- Quick Action Buttons -->
              <div class="flex space-x-3 pt-4 border-t border-gray-100">
                <button @click="quickApprove(order)" 
                        class="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md">
                  <span class="flex items-center justify-center">
                    <span class="text-lg mr-2">✅</span>
                    Quick Approve
                  </span>
                </button>
                <button @click="showRejectModal(order)" 
                        class="flex-1 bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md">
                  <span class="flex items-center justify-center">
                    <span class="text-lg mr-2">❌</span>
                    Reject
                  </span>
                </button>
                <button @click="viewOrderDetails(order)" 
                        class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md">
                  <span class="text-lg">👁️</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Pending Orders -->
      <div v-else class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-8">
        <div class="text-center py-12">
          <div class="text-6xl mb-4">🎉</div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">All Caught Up!</h3>
          <p class="text-gray-500">No pending orders to review. Great job!</p>
        </div>
      </div>

      <!-- All Orders Management -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-gray-800 to-gray-900 px-6 py-4 flex justify-between items-center">
          <div>
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">📋</span>
              All Orders Management
            </h2>
            <p class="text-gray-300 text-sm mt-1">Complete order history and management</p>
          </div>
          <div class="flex space-x-3">
            <select v-model="statusFilter" class="bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 text-sm">
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="completed">Completed</option>
            </select>
            <button @click="exportApprovedCSV" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              📊 Export Approved
            </button>
          </div>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="order in filteredOrders" :key="order.id" 
                  class="hover:bg-gray-50 transition-colors"
                  :class="{ 'opacity-50 bg-red-50': order.status === 'deleted' }">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
                  <div class="text-sm text-gray-500">${{ order.price }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ order.clientName }}</div>
                  <div class="text-sm text-gray-500">{{ order.clientType }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ order.potatoType }} - {{ order.cutShape }}</div>
                  <div class="text-sm text-gray-500">{{ order.bags }} bags</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ formatDate(order.deliveryDate) }}</div>
                  <div class="text-sm" :class="getUrgencyClass(order.deliveryDate)">{{ getTimeLeft(order.deliveryDate) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(order.status)" class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full">
                    {{ getStatusText(order.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button v-if="order.status === 'pending'" @click="quickApprove(order)" 
                          class="text-green-600 hover:text-green-900 font-medium">Approve</button>
                  <button v-if="order.status === 'pending'" @click="showRejectModal(order)" 
                          class="text-red-600 hover:text-red-900 font-medium">Reject</button>
                  <button @click="overrideOrder(order)" 
                          class="text-purple-600 hover:text-purple-900 font-medium">Override</button>
                  <button @click="viewOrderDetails(order)" 
                          class="text-gray-600 hover:text-gray-900 font-medium">Details</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>

    <!-- Reject Modal -->
    <div v-if="showRejectDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl">
        <h3 class="text-lg font-bold text-gray-900 mb-4">Reject Order #{{ selectedOrder?.id }}</h3>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Rejection Reason</label>
          <select v-model="rejectionReason" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500">
            <option value="">Select reason...</option>
            <option value="insufficient_stock">Insufficient Stock</option>
            <option value="delivery_unavailable">Delivery Date Unavailable</option>
            <option value="quality_concerns">Quality Concerns</option>
            <option value="pricing_issue">Pricing Issue</option>
            <option value="other">Other</option>
          </select>
        </div>
        <div v-if="rejectionReason === 'other'" class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Custom Reason</label>
          <textarea v-model="customReason" rows="3" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500"
                    placeholder="Please specify the reason..."></textarea>
        </div>
        <div class="flex space-x-3">
          <button @click="confirmReject" 
                  class="flex-1 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
            Confirm Reject
          </button>
          <button @click="closeRejectModal" 
                  class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'
import { useOrderStore } from '@/stores/orderStore'

const router = useRouter()
const authStore = useSimpleAuthStore()
const orderStore = useOrderStore()
const { user } = authStore

// State
const statusFilter = ref('')
const showRejectDialog = ref(false)
const selectedOrder = ref(null)
const rejectionReason = ref('')
const customReason = ref('')

// Computed properties
const pendingOrders = computed(() => orderStore.pendingOrders)
const allOrders = computed(() => orderStore.orders)

const filteredOrders = computed(() => {
  let orders = allOrders.value
  if (statusFilter.value) {
    orders = orders.filter(order => order.status === statusFilter.value)
  }
  return orders.sort((a, b) => {
    // Sort by priority first, then by creation date
    const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
    const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 0
    const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 0

    if (aPriority !== bPriority) return bPriority - aPriority
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  })
})

const approvedToday = computed(() => {
  const today = new Date().toDateString()
  return allOrders.value.filter(order =>
    order.status === 'approved' &&
    order.approvedAt &&
    new Date(order.approvedAt).toDateString() === today
  ).length
})

const pendingRevenue = computed(() => {
  return pendingOrders.value.reduce((sum, order) => sum + order.price, 0)
})

const approvalRate = computed(() => {
  const thisMonth = new Date().getMonth()
  const thisMonthOrders = allOrders.value.filter(order =>
    new Date(order.createdAt).getMonth() === thisMonth
  )
  const approved = thisMonthOrders.filter(order => order.status === 'approved').length
  return thisMonthOrders.length > 0 ? Math.round((approved / thisMonthOrders.length) * 100) : 0
})

// Methods
const logout = () => {
  authStore.logout()
  router.push('/')
}

const quickApprove = async (order: any) => {
  try {
    await orderStore.approveOrder(order.id, user?.id || '')
    // Show success notification
    showNotification(`✅ Order #${order.id} approved successfully!`, 'success')
  } catch (error) {
    showNotification(`❌ Failed to approve order #${order.id}`, 'error')
  }
}

const showRejectModal = (order: any) => {
  selectedOrder.value = order
  showRejectDialog.value = true
  rejectionReason.value = ''
  customReason.value = ''
}

const closeRejectModal = () => {
  showRejectDialog.value = false
  selectedOrder.value = null
  rejectionReason.value = ''
  customReason.value = ''
}

const confirmReject = async () => {
  if (!selectedOrder.value || !rejectionReason.value) return

  const reason = rejectionReason.value === 'other' ? customReason.value : rejectionReason.value

  try {
    await orderStore.rejectOrder(selectedOrder.value.id, reason)
    showNotification(`❌ Order #${selectedOrder.value.id} rejected`, 'warning')
    closeRejectModal()
  } catch (error) {
    showNotification(`❌ Failed to reject order`, 'error')
  }
}

const overrideOrder = (order: any) => {
  // Sales manager override functionality
  const action = prompt(`Override action for Order #${order.id}:\n\n1. Change status\n2. Modify details\n3. Add flag\n\nEnter action number:`)

  if (action === '1') {
    const newStatus = prompt('New status (pending/approved/rejected/in_production/completed):')
    if (newStatus) {
      orderStore.updateOrder(order.id, { status: newStatus })
      showNotification(`🔄 Order #${order.id} status changed to ${newStatus}`, 'info')
    }
  } else if (action === '3') {
    const flag = prompt('Add flag/note:')
    if (flag) {
      orderStore.updateOrder(order.id, { flag })
      showNotification(`🏷️ Flag added to Order #${order.id}`, 'info')
    }
  }
}

const viewOrderDetails = (order: any) => {
  const details = `
Order Details - #${order.id}

Client: ${order.clientName} (${order.clientType})
Product: ${order.potatoType} - ${order.cutShape}
Quantity: ${order.bags} bags (${order.bags * 25}kg)
Price: $${order.price}
Delivery Date: ${formatDate(order.deliveryDate)}
Priority: ${order.priority.toUpperCase()}

Status: ${getStatusText(order.status)}
Created: ${formatDate(order.createdAt)}
${order.approvedBy ? `Approved by: ${order.approvedBy} on ${formatDate(order.approvedAt)}` : ''}
${order.rejectionReason ? `Rejection Reason: ${order.rejectionReason}` : ''}

Notes: ${order.notes || 'None'}
${order.flag ? `Flag: ${order.flag}` : ''}
  `.trim()

  alert(details)
}

const exportApprovedCSV = () => {
  const approvedOrders = allOrders.value.filter(order => order.status === 'approved')

  if (approvedOrders.length === 0) {
    alert('No approved orders to export')
    return
  }

  const headers = ['Order ID', 'Client', 'Product', 'Cut Shape', 'Bags', 'Price', 'Delivery Date', 'Approved Date', 'Notes']
  const csvContent = [
    headers.join(','),
    ...approvedOrders.map(order => [
      order.id,
      `"${order.clientName}"`,
      order.potatoType,
      order.cutShape,
      order.bags,
      order.price,
      order.deliveryDate,
      order.approvedAt || '',
      `"${order.notes || ''}"`
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `approved-orders-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)

  showNotification('📊 CSV exported successfully!', 'success')
}

const getPriorityClass = (order: any) => {
  const classes = {
    urgent: 'bg-red-100 text-red-800',
    high: 'bg-orange-100 text-orange-800',
    medium: 'bg-yellow-100 text-yellow-800',
    low: 'bg-green-100 text-green-800'
  }
  return classes[order.priority as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getPriorityBorderClass = (order: any) => {
  const classes = {
    urgent: 'border-red-300 bg-red-50',
    high: 'border-orange-300 bg-orange-50',
    medium: 'border-yellow-300 bg-yellow-50',
    low: 'border-green-300 bg-green-50'
  }
  return classes[order.priority as keyof typeof classes] || 'border-gray-300'
}

const getPriority = (order: any) => {
  const priorities = {
    urgent: '🚨 URGENT',
    high: '🔥 HIGH',
    medium: '📋 MEDIUM',
    low: '📝 LOW'
  }
  return priorities[order.priority as keyof typeof priorities] || order.priority
}

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    in_production: 'bg-blue-100 text-blue-800',
    completed: 'bg-purple-100 text-purple-800',
    delivered: 'bg-indigo-100 text-indigo-800',
    deleted: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '⏳ Pending',
    approved: '✅ Approved',
    rejected: '❌ Rejected',
    in_production: '🏭 Production',
    completed: '✅ Completed',
    delivered: '🚚 Delivered',
    deleted: '🗑️ Deleted'
  }
  return texts[status as keyof typeof texts] || status
}

const getTimeLeft = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'Overdue'
  if (days === 0) return 'Today'
  if (days === 1) return 'Tomorrow'
  return `${days} days`
}

const getUrgencyClass = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'text-red-600 font-bold'
  if (days <= 1) return 'text-orange-600 font-medium'
  if (days <= 3) return 'text-yellow-600'
  return 'text-gray-500'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const showNotification = (message: string, type: 'success' | 'error' | 'warning' | 'info') => {
  // Simple notification - in a real app, you'd use a proper notification system
  const colors = {
    success: '#10B981',
    error: '#EF4444',
    warning: '#F59E0B',
    info: '#3B82F6'
  }

  console.log(`%c${message}`, `color: ${colors[type]}; font-weight: bold;`)

  // Show browser notification if permission granted
  if (Notification.permission === 'granted') {
    new Notification('Verto Sales Manager', {
      body: message,
      icon: '/favicon.ico'
    })
  }
}

// Request notification permission on mount
onMounted(() => {
  if (Notification.permission === 'default') {
    Notification.requestPermission()
  }
})
</script>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'
import { useOrderStore } from '@/stores/orderStore'

const router = useRouter()
const authStore = useSimpleAuthStore()
const orderStore = useOrderStore()
const { user } = authStore

// State
const statusFilter = ref('')
const showRejectDialog = ref(false)
const selectedOrder = ref(null)
const rejectionReason = ref('')
const customReason = ref('')

// Computed properties
const pendingOrders = computed(() => orderStore.pendingOrders)
const approvedToday = computed(() => {
  const today = new Date().toDateString()
  return orderStore.orders.filter(o =>
    o.status === 'approved' &&
    o.approvedAt &&
    new Date(o.approvedAt).toDateString() === today
  ).length
})

const pendingRevenue = computed(() =>
  pendingOrders.value.reduce((sum, order) => sum + order.price, 0)
)

const approvalRate = computed(() => {
  const thisMonth = new Date().getMonth()
  const monthlyOrders = orderStore.orders.filter(o =>
    new Date(o.createdAt).getMonth() === thisMonth
  )
  const approved = monthlyOrders.filter(o => o.status === 'approved').length
  return monthlyOrders.length > 0 ? Math.round((approved / monthlyOrders.length) * 100) : 0
})

const filteredOrders = computed(() => {
  let orders = orderStore.orders
  if (statusFilter.value) {
    orders = orders.filter(o => o.status === statusFilter.value)
  }
  return orders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
})

// Methods
const logout = () => {
  authStore.logout()
  router.push('/')
}

const quickApprove = async (order: any) => {
  try {
    await orderStore.approveOrder(order.id, user?.id || '')
    // Show success notification
    console.log(`✅ Order ${order.id} approved successfully`)
  } catch (error) {
    alert('❌ Failed to approve order. Please try again.')
  }
}

const showRejectModal = (order: any) => {
  selectedOrder.value = order
  showRejectDialog.value = true
  rejectionReason.value = ''
  customReason.value = ''
}

const closeRejectModal = () => {
  showRejectDialog.value = false
  selectedOrder.value = null
  rejectionReason.value = ''
  customReason.value = ''
}

const confirmReject = async () => {
  if (!rejectionReason.value) {
    alert('Please select a rejection reason')
    return
  }

  const reason = rejectionReason.value === 'other' ? customReason.value : rejectionReason.value

  try {
    await orderStore.rejectOrder(selectedOrder.value.id, reason)
    closeRejectModal()
    console.log(`❌ Order ${selectedOrder.value.id} rejected: ${reason}`)
  } catch (error) {
    alert('❌ Failed to reject order. Please try again.')
  }
}

const overrideOrder = (order: any) => {
  const action = prompt(`Override order ${order.id}. Enter new status:`, order.status)
  if (action && action !== order.status) {
    orderStore.updateOrder(order.id, { status: action })
    console.log(`🔧 Order ${order.id} status overridden to: ${action}`)
  }
}

const viewOrderDetails = (order: any) => {
  const details = `
Order Details:

Order ID: ${order.id}
Client: ${order.clientName} (${order.clientType})
Product: ${order.potatoType} - ${order.cutShape}
Quantity: ${order.bags} bags (${order.bags * 25}kg)
Delivery Date: ${formatDate(order.deliveryDate)}
Status: ${getStatusText(order.status)}
Priority: ${getPriority(order)}
Price: $${order.price}
Notes: ${order.notes || 'None'}

Created: ${formatDate(order.createdAt)}
Updated: ${formatDate(order.updatedAt)}
${order.approvedBy ? `Approved by: ${order.approvedBy} on ${formatDate(order.approvedAt)}` : ''}
${order.rejectionReason ? `Rejection reason: ${order.rejectionReason}` : ''}
  `
  alert(details)
}

const exportApprovedCSV = () => {
  const approvedOrders = orderStore.orders.filter(o => o.status === 'approved')

  const csvContent = [
    ['Order ID', 'Client', 'Product', 'Cut Shape', 'Bags', 'Weight (kg)', 'Price', 'Delivery Date', 'Approved Date', 'Approved By'].join(','),
    ...approvedOrders.map(order => [
      order.id,
      order.clientName,
      order.potatoType,
      order.cutShape,
      order.bags,
      order.bags * 25,
      order.price,
      order.deliveryDate,
      order.approvedAt || '',
      order.approvedBy || ''
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `approved-orders-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
}

const getPriority = (order: any) => {
  const deliveryDays = Math.ceil((new Date(order.deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (deliveryDays <= 1) return 'URGENT'
  if (deliveryDays <= 3) return 'HIGH'
  if (order.bags >= 100) return 'HIGH'
  return 'NORMAL'
}

const getPriorityClass = (order: any) => {
  const priority = getPriority(order)
  const classes = {
    URGENT: 'bg-red-100 text-red-800',
    HIGH: 'bg-orange-100 text-orange-800',
    NORMAL: 'bg-blue-100 text-blue-800'
  }
  return classes[priority as keyof typeof classes]
}

const getPriorityBorderClass = (order: any) => {
  const priority = getPriority(order)
  const classes = {
    URGENT: 'border-red-300 bg-red-50',
    HIGH: 'border-orange-300 bg-orange-50',
    NORMAL: 'border-blue-300 bg-blue-50'
  }
  return classes[priority as keyof typeof classes]
}

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    in_production: 'bg-blue-100 text-blue-800',
    completed: 'bg-purple-100 text-purple-800',
    delivered: 'bg-indigo-100 text-indigo-800',
    deleted: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '⏳ Pending',
    approved: '✅ Approved',
    rejected: '❌ Rejected',
    in_production: '🏭 Production',
    completed: '✅ Completed',
    delivered: '🚚 Delivered',
    deleted: '🗑️ Cancelled'
  }
  return texts[status as keyof typeof texts] || status
}

const getTimeLeft = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'Overdue'
  if (days === 0) return 'Today'
  if (days === 1) return 'Tomorrow'
  return `${days} days`
}

const getUrgencyClass = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'text-red-600 font-bold'
  if (days <= 1) return 'text-orange-600 font-medium'
  if (days <= 3) return 'text-yellow-600'
  return 'text-gray-500'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
