# Verto Order System

A comprehensive web-based order automation system for Verto, a fresh potato factory in Kfarnabrakh, Lebanon. This system digitizes the order workflow and eliminates WhatsApp messaging by providing secure dashboards for both factory staff and clients.

## 🚀 Features

### Current Implementation (Phase 1)
- ✅ **Project Setup & Architecture**: Vue 3 + TypeScript + TailwindCSS + Firebase
- ✅ **Firebase Backend Configuration**: Firestore security rules, Functions, triggers
- ✅ **User Authentication System**: Login/registration with role-based access control
- ✅ **Basic Dashboard Views**: Client and factory dashboards with navigation

### Upcoming Features (Next Phases)
- 🔄 **Client Dashboard Development**: Order submission, status tracking, history
- 🔄 **Factory Dashboard Development**: Live order feed, approval system, role-based UI
- 🔄 **Firebase Functions Backend Logic**: Order processing workflows, notifications
- 🔄 **Google Sheets Integration**: Automated sync, export functionality
- 🔄 **Analytics & Reporting**: Order metrics, client analysis, rejection tracking
- 🔄 **Mobile Optimization & UI Polish**: Responsive design, loading states
- 🔄 **Testing & Documentation**: Comprehensive testing and setup guides

## 🏗️ Architecture

### Frontend Stack
- **Vue 3** with Composition API and TypeScript
- **Vite** for build tooling and development server
- **TailwindCSS** for styling with custom design system
- **Pinia** for state management
- **Vue Router 4** for routing with navigation guards

### Backend Stack
- **Firebase Firestore** for database with security rules
- **Firebase Authentication** for user management
- **Firebase Functions** for serverless backend logic
- **Firebase Hosting** for deployment

### User Roles
- **Client Users**: Submit orders, track status, view history
- **Sales Manager**: Review orders, communicate with clients
- **General Manager**: Approve/reject orders, oversee operations
- **Ops Manager**: Manage logistics and delivery scheduling
- **Admin**: Full system access and user management

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+ and npm
- Firebase CLI (`npm install -g firebase-tools`)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd VertoSoft
   ```

2. **Install dependencies**
   ```bash
   npm install
   cd functions && npm install && cd ..
   ```

3. **Firebase Configuration**
   ```bash
   # Login to Firebase
   firebase login

   # Initialize Firebase project (if not already done)
   firebase init

   # Create environment file
   cp .env.example .env.local
   ```

4. **Configure environment variables**
   Edit `.env.local` with your Firebase configuration:
   ```env
   VITE_FIREBASE_API_KEY=your_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

6. **Start Firebase emulators (optional)**
   ```bash
   firebase emulators:start
   ```

## 📁 Project Structure

```
VertoSoft/
├── src/
│   ├── components/          # Reusable Vue components
│   │   └── layout/         # Layout components (AppLayout, Navigation)
│   ├── views/              # Page components
│   │   ├── auth/           # Authentication pages (Login, Register)
│   │   ├── client/         # Client dashboard pages
│   │   └── factory/        # Factory dashboard pages
│   ├── stores/             # Pinia stores
│   │   └── auth.ts         # Authentication store
│   ├── services/           # API and service layers
│   │   ├── firebase.ts     # Firebase configuration
│   │   └── auth.ts         # Authentication service
│   ├── types/              # TypeScript type definitions
│   │   ├── auth.ts         # User and authentication types
│   │   └── orders.ts       # Order-related types
│   ├── router/             # Vue Router configuration
│   └── assets/             # Static assets
├── functions/              # Firebase Functions
│   ├── src/
│   │   ├── triggers/       # Firestore triggers
│   │   └── services/       # Backend services
│   └── package.json
├── firestore.rules         # Firestore security rules
├── firebase.json           # Firebase configuration
└── package.json
```

## 🔐 Security

### Firestore Security Rules
- Role-based access control for all collections
- Users can only access their own data
- Factory staff have appropriate permissions based on role
- Audit logging for all sensitive operations

### Authentication
- Firebase Authentication with email/password
- Role-based route protection
- Secure user profile management
- Password reset functionality (planned)

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
firebase deploy
```

### Functions Deployment
```bash
cd functions
npm run build
firebase deploy --only functions
```

## 📊 Database Schema

### Collections
- **users**: User profiles with role-based permissions
- **orders**: Order documents with full lifecycle tracking
- **products**: Product catalog and pricing
- **notifications**: In-app notification system
- **logs**: Audit trail for all system actions
- **analytics**: Daily/monthly performance metrics

## 🔧 Configuration

### Environment Variables
- `VITE_FIREBASE_*`: Firebase configuration
- `VITE_APP_NAME`: Application name
- `VITE_APP_VERSION`: Application version

### Firebase Functions Environment
- Email service configuration (Nodemailer)
- Google Sheets API credentials (planned)
- WhatsApp API configuration (planned)

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run e2e tests
npm run test:e2e

# Run Firebase emulator tests
npm run test:firebase
```

## 📝 Contributing

1. Create a feature branch from `main`
2. Make your changes following the established patterns
3. Test thoroughly using Firebase emulators
4. Submit a pull request with detailed description

## 📞 Support

For technical support or questions about the Verto Order System, please contact the development team.

## 📄 License

This project is proprietary software developed for Verto Factory, Kfarnabrakh, Lebanon.
