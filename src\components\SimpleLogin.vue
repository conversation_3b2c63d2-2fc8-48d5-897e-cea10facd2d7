<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
      <div class="text-center mb-8">
        <div class="h-12 w-12 bg-blue-600 rounded-md flex items-center justify-center mx-auto mb-4">
          <span class="text-white font-bold text-xl">V</span>
        </div>
        <h1 class="text-2xl font-bold text-gray-900">Verto Order System</h1>
        <p class="text-gray-600 mt-2">Fresh Potato Factory - Lebanon</p>
      </div>

      <form @submit.prevent="handleLogin" class="space-y-6">
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
          <input
            id="email"
            v-model="email"
            type="email"
            required
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your email"
          />
        </div>

        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
          <input
            id="password"
            v-model="password"
            type="password"
            required
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your password"
          />
        </div>

        <button
          type="submit"
          :disabled="loading"
          class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
        >
          <span v-if="loading">Signing in...</span>
          <span v-else>Sign In</span>
        </button>
      </form>

      <div v-if="error" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
        {{ error }}
      </div>

      <div class="mt-6 text-center">
        <p class="text-sm text-gray-600">
          Don't have an account?
          <button @click="showRegister" class="font-medium text-blue-600 hover:text-blue-500">
            Sign up
          </button>
        </p>
      </div>

      <div class="mt-8 pt-6 border-t border-gray-200">
        <div class="text-xs text-gray-500 text-center">
          <p>✅ Vue 3 + TailwindCSS Working</p>
          <p>✅ Component System Working</p>
          <p>✅ Form Handling Working</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'

const router = useRouter()
const authStore = useSimpleAuthStore()

const email = ref('')
const password = ref('')

const { loading, error } = authStore

const handleLogin = async () => {
  if (!email.value || !password.value) {
    alert('Please enter both email and password')
    return
  }

  try {
    await authStore.login(email.value, password.value)
    router.push('/dashboard')
  } catch (err) {
    // Error is handled by the store
  }
}

const showRegister = () => {
  alert('Registration form would open here')
}
</script>
