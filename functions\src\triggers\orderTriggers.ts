import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { notificationService } from '../services/notificationService';
import { analyticsService } from '../services/analyticsService';
import { auditService } from '../services/auditService';

const db = admin.firestore();

export const orderTriggers = {
  // Trigger when a new order is created
  onOrderCreated: functions.firestore
    .document('orders/{orderId}')
    .onCreate(async (snap, context) => {
      const order = snap.data();
      const orderId = context.params.orderId;

      try {
        // Generate order number if not present
        if (!order.orderNumber) {
          const orderNumber = await generateOrderNumber();
          await snap.ref.update({ orderNumber });
        }

        // Log the order creation
        await auditService.logAction({
          action: 'order_created',
          userId: order.clientId,
          orderId: orderId,
          details: {
            clientName: order.clientName,
            totalQuantity: order.totalQuantity,
            itemCount: order.items.length
          }
        });

        // Notify sales managers about new order
        const salesManagers = await getUsersByRole('sales_manager');
        if (salesManagers.length > 0) {
          await notificationService.sendNotification(
            'new_order',
            salesManagers.map(user => user.email),
            `New order #${order.orderNumber || orderId} from ${order.clientName}`,
            orderId
          );
        }

        // Update analytics
        await analyticsService.updateOrderStats('created');

        console.log(`Order ${orderId} created successfully`);
      } catch (error) {
        console.error('Error processing order creation:', error);
      }
    }),

  // Trigger when an order is updated
  onOrderUpdated: functions.firestore
    .document('orders/{orderId}')
    .onUpdate(async (change, context) => {
      const before = change.before.data();
      const after = change.after.data();
      const orderId = context.params.orderId;

      try {
        // Check if status changed
        if (before.status !== after.status) {
          await handleStatusChange(orderId, before, after);
        }

        // Log the update
        await auditService.logAction({
          action: 'order_updated',
          userId: after.updatedBy || 'system',
          orderId: orderId,
          details: {
            changes: getChangedFields(before, after),
            oldStatus: before.status,
            newStatus: after.status
          }
        });

        console.log(`Order ${orderId} updated successfully`);
      } catch (error) {
        console.error('Error processing order update:', error);
      }
    }),

  // Specific trigger for status changes
  onOrderStatusChanged: functions.firestore
    .document('orders/{orderId}')
    .onUpdate(async (change, context) => {
      const before = change.before.data();
      const after = change.after.data();
      const orderId = context.params.orderId;

      // Only process if status actually changed
      if (before.status === after.status) {
        return;
      }

      try {
        await handleStatusChange(orderId, before, after);
      } catch (error) {
        console.error('Error handling status change:', error);
      }
    })
};

// Helper function to handle status changes
async function handleStatusChange(orderId: string, before: any, after: any) {
  const status = after.status;
  const clientEmail = after.clientEmail;
  const orderNumber = after.orderNumber || orderId;

  // Send notifications based on status
  switch (status) {
    case 'approved':
      await notificationService.sendNotification(
        'order_approved',
        [clientEmail],
        `Your order #${orderNumber} has been approved and is being processed.`,
        orderId
      );
      await analyticsService.updateOrderStats('approved');
      break;

    case 'rejected':
      await notificationService.sendNotification(
        'order_rejected',
        [clientEmail],
        `Your order #${orderNumber} has been rejected. Reason: ${after.rejectionReason || 'Not specified'}`,
        orderId
      );
      await analyticsService.updateOrderStats('rejected');
      break;

    case 'delivered':
      await notificationService.sendNotification(
        'order_delivered',
        [clientEmail],
        `Your order #${orderNumber} has been delivered successfully.`,
        orderId
      );
      await analyticsService.updateOrderStats('delivered');
      break;

    case 'cancelled':
      await notificationService.sendNotification(
        'order_cancelled',
        [clientEmail],
        `Your order #${orderNumber} has been cancelled.`,
        orderId
      );
      await analyticsService.updateOrderStats('cancelled');
      break;
  }

  // Update timestamps
  const updates: any = {
    updatedAt: admin.firestore.FieldValue.serverTimestamp()
  };

  if (status === 'approved' && after.approvedBy) {
    updates.approvedAt = admin.firestore.FieldValue.serverTimestamp();
  }

  await db.collection('orders').doc(orderId).update(updates);
}

// Helper function to generate order number
async function generateOrderNumber(): Promise<string> {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  
  const prefix = `VO${year}${month}${day}`;
  
  // Get the count of orders created today
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);
  
  const todayOrders = await db.collection('orders')
    .where('createdAt', '>=', startOfDay)
    .where('createdAt', '<', endOfDay)
    .get();
  
  const sequence = String(todayOrders.size + 1).padStart(3, '0');
  
  return `${prefix}-${sequence}`;
}

// Helper function to get users by role
async function getUsersByRole(role: string) {
  const users = await db.collection('users')
    .where('role', '==', role)
    .where('isActive', '==', true)
    .get();
  
  return users.docs.map(doc => doc.data());
}

// Helper function to get changed fields
function getChangedFields(before: any, after: any): string[] {
  const changes: string[] = [];
  const fields = ['status', 'items', 'requestedDeliveryDate', 'clientNotes', 'internalNotes', 'priority', 'tags'];
  
  fields.forEach(field => {
    if (JSON.stringify(before[field]) !== JSON.stringify(after[field])) {
      changes.push(field);
    }
  });
  
  return changes;
}
