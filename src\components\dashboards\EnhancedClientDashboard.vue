<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-indigo-50">
    <!-- Enhanced Header -->
    <nav class="bg-white/90 backdrop-blur-sm shadow-lg border-b border-green-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-10 w-10 bg-gradient-to-r from-green-600 to-green-700 rounded-xl flex items-center justify-center shadow-lg">
              <span class="text-white font-bold text-lg">🥔</span>
            </div>
            <div class="ml-3">
              <span class="text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Verto</span>
              <div class="text-sm text-green-600 font-medium">Fresh Potato Orders</div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <!-- Real-time indicator -->
            <div class="flex items-center space-x-2 bg-green-50 px-3 py-1 rounded-full">
              <div class="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-green-700 font-medium">Live Updates</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">{{ user?.name }}</div>
              <div class="text-xs text-gray-500">{{ user?.email }}</div>
            </div>
            <span class="bg-gradient-to-r from-green-100 to-green-200 text-green-800 px-4 py-2 rounded-full font-medium text-sm shadow-sm">
              🍽️ Restaurant Client
            </span>
            <button @click="logout" class="bg-red-50 hover:bg-red-100 text-red-600 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md">
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      
      <!-- Welcome Section -->
      <div class="mb-8">
        <div class="bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl p-6 text-white shadow-xl">
          <h1 class="text-2xl font-bold mb-2">Welcome back, {{ user?.name }}! 👋</h1>
          <p class="text-green-100">Ready to place your fresh potato order? We're here to serve your restaurant's needs.</p>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg">
              <span class="text-2xl">📦</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Orders</p>
              <p class="text-2xl font-bold text-gray-900">{{ userOrders.length }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-yellow-100 rounded-lg">
              <span class="text-2xl">⏳</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Pending</p>
              <p class="text-2xl font-bold text-yellow-600">{{ pendingCount }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-lg">
              <span class="text-2xl">✅</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Approved</p>
              <p class="text-2xl font-bold text-green-600">{{ approvedCount }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-purple-100 rounded-lg">
              <span class="text-2xl">🚚</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">This Month</p>
              <p class="text-2xl font-bold text-purple-600">{{ monthlyOrders }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Order Section -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- Order Form -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-green-600 to-blue-600 px-6 py-4">
              <h2 class="text-xl font-bold text-white flex items-center">
                <span class="text-2xl mr-2">🛒</span>
                Place New Order
              </h2>
              <p class="text-green-100 text-sm mt-1">Quick and easy potato ordering for your restaurant</p>
            </div>
            
            <form @submit.prevent="submitOrder" class="p-6 space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Potato Type -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Potato Type</label>
                  <select v-model="orderForm.potatoType" required 
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all">
                    <option value="">Select potato type</option>
                    <option value="russet">🥔 Russet - Perfect for fries</option>
                    <option value="red">🔴 Red - Great for roasting</option>
                    <option value="yukon">🟡 Yukon Gold - Versatile choice</option>
                    <option value="fingerling">👆 Fingerling - Gourmet option</option>
                  </select>
                </div>

                <!-- Cut Shape -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Cut Shape</label>
                  <select v-model="orderForm.cutShape" required 
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all">
                    <option value="">Select cut shape</option>
                    <option value="cubes">🔲 Cubes - 1cm diced</option>
                    <option value="long">📏 Long strips - French fry cut</option>
                    <option value="wedges">🔺 Wedges - Thick cut</option>
                    <option value="whole">⭕ Whole - Uncut potatoes</option>
                  </select>
                </div>

                <!-- Quantity -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Number of Bags (25kg each)</label>
                  <input v-model.number="orderForm.bags" type="number" min="1" max="500" required
                         class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                         placeholder="Enter quantity">
                  <p class="text-sm text-gray-500 mt-1">Total weight: {{ orderForm.bags * 25 }}kg</p>
                </div>

                <!-- Delivery Date -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Delivery Date</label>
                  <input v-model="orderForm.deliveryDate" type="date" required :min="minDate"
                         class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all">
                </div>
              </div>

              <!-- Notes -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Special Instructions (Optional)</label>
                <textarea v-model="orderForm.notes" rows="3" 
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                          placeholder="Any special requirements or notes..."></textarea>
              </div>

              <!-- Price Estimate -->
              <div v-if="estimatedPrice > 0" class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex justify-between items-center">
                  <span class="text-green-800 font-medium">Estimated Total:</span>
                  <span class="text-2xl font-bold text-green-600">${{ estimatedPrice }}</span>
                </div>
                <p class="text-sm text-green-600 mt-1">Final price may vary based on market conditions</p>
              </div>

              <!-- Submit Button -->
              <button type="submit" :disabled="loading" 
                      class="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed">
                <span v-if="loading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing Order...
                </span>
                <span v-else class="flex items-center justify-center">
                  <span class="text-xl mr-2">🚀</span>
                  Place Order Now
                </span>
              </button>
            </form>
          </div>
        </div>

        <!-- Quick Actions & Tips -->
        <div class="space-y-6">
          <!-- Quick Reorder -->
          <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
              <span class="text-xl mr-2">⚡</span>
              Quick Reorder
            </h3>
            <div v-if="recentOrders.length > 0" class="space-y-3">
              <div v-for="order in recentOrders.slice(0, 2)" :key="order.id" 
                   class="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
                   @click="reorderItem(order)">
                <div class="text-sm font-medium text-gray-900">{{ order.potatoType }} - {{ order.cutShape }}</div>
                <div class="text-xs text-gray-500">{{ order.bags }} bags • ${{ order.price }}</div>
              </div>
            </div>
            <div v-else class="text-sm text-gray-500 text-center py-4">
              No recent orders to reorder
            </div>
          </div>

          <!-- Order Tips -->
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200 p-6">
            <h3 class="text-lg font-bold text-blue-900 mb-4 flex items-center">
              <span class="text-xl mr-2">💡</span>
              Pro Tips
            </h3>
            <div class="space-y-3 text-sm">
              <div class="flex items-start space-x-2">
                <span class="text-green-500 mt-0.5">✓</span>
                <span class="text-blue-800">Order 2+ days ahead for guaranteed delivery</span>
              </div>
              <div class="flex items-start space-x-2">
                <span class="text-green-500 mt-0.5">✓</span>
                <span class="text-blue-800">Bulk orders (100+ bags) get priority processing</span>
              </div>
              <div class="flex items-start space-x-2">
                <span class="text-green-500 mt-0.5">✓</span>
                <span class="text-blue-800">Russet potatoes are perfect for crispy fries</span>
              </div>
            </div>
          </div>

          <!-- Contact Support -->
          <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
              <span class="text-xl mr-2">📞</span>
              Need Help?
            </h3>
            <div class="space-y-3">
              <button class="w-full bg-green-50 hover:bg-green-100 text-green-700 font-medium py-2 px-4 rounded-lg transition-colors">
                📱 Call Sales Team
              </button>
              <button class="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 font-medium py-2 px-4 rounded-lg transition-colors">
                💬 Live Chat Support
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Orders -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-gray-800 to-gray-900 px-6 py-4">
          <h2 class="text-xl font-bold text-white flex items-center">
            <span class="text-2xl mr-2">📋</span>
            Your Recent Orders
          </h2>
          <p class="text-gray-300 text-sm mt-1">Track your order status and history</p>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Details</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="order in userOrders" :key="order.id" class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
                  <div class="text-sm text-gray-500">${{ order.price }} • {{ formatDate(order.createdAt) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ order.potatoType }} - {{ order.cutShape }}</div>
                  <div class="text-sm text-gray-500">{{ order.bags }} bags ({{ order.bags * 25 }}kg)</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ formatDate(order.deliveryDate) }}</div>
                  <div class="text-sm" :class="getUrgencyClass(order.deliveryDate)">{{ getTimeLeft(order.deliveryDate) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(order.status)" class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full">
                    {{ getStatusText(order.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button v-if="order.status === 'pending'" @click="editOrder(order)" 
                          class="text-blue-600 hover:text-blue-900 font-medium">Edit</button>
                  <button @click="reorderItem(order)" 
                          class="text-green-600 hover:text-green-900 font-medium">Reorder</button>
                  <button @click="viewOrderDetails(order)" 
                          class="text-gray-600 hover:text-gray-900 font-medium">Details</button>
                </td>
              </tr>
            </tbody>
          </table>
          
          <div v-if="userOrders.length === 0" class="text-center py-12">
            <div class="text-6xl mb-4">📦</div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No orders yet</h3>
            <p class="text-gray-500">Place your first order to get started!</p>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'
import { useOrderStore } from '@/stores/orderStore'

const router = useRouter()
const authStore = useSimpleAuthStore()
const orderStore = useOrderStore()
const { user } = authStore

// Form state
const orderForm = ref({
  potatoType: '',
  cutShape: '',
  bags: 1,
  deliveryDate: '',
  notes: ''
})

const loading = ref(false)

// Computed properties
const userOrders = computed(() => orderStore.userOrders)
const pendingCount = computed(() => userOrders.value.filter(o => o.status === 'pending').length)
const approvedCount = computed(() => userOrders.value.filter(o => o.status === 'approved').length)
const monthlyOrders = computed(() => {
  const thisMonth = new Date().getMonth()
  return userOrders.value.filter(o => new Date(o.createdAt).getMonth() === thisMonth).length
})

const recentOrders = computed(() =>
  userOrders.value
    .filter(o => o.status === 'completed')
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 3)
)

const estimatedPrice = computed(() => {
  if (!orderForm.value.bags || !orderForm.value.potatoType) return 0
  const basePrice = 5 // $5 per bag base price
  const typeMultiplier = {
    russet: 1.0,
    red: 1.1,
    yukon: 1.2,
    fingerling: 1.5
  }
  return Math.round(orderForm.value.bags * basePrice * (typeMultiplier[orderForm.value.potatoType as keyof typeof typeMultiplier] || 1))
})

const minDate = computed(() => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().split('T')[0]
})

// Methods
const logout = () => {
  authStore.logout()
  router.push('/')
}

const submitOrder = async () => {
  loading.value = true
  try {
    await orderStore.createOrder({
      ...orderForm.value,
      price: estimatedPrice.value
    })

    // Reset form
    orderForm.value = {
      potatoType: '',
      cutShape: '',
      bags: 1,
      deliveryDate: '',
      notes: ''
    }

    // Show success notification
    alert('🎉 Order placed successfully! You will receive confirmation once approved.')
  } catch (error) {
    alert('❌ Failed to place order. Please try again.')
  } finally {
    loading.value = false
  }
}

const editOrder = (order: any) => {
  orderForm.value = {
    potatoType: order.potatoType,
    cutShape: order.cutShape,
    bags: order.bags,
    deliveryDate: order.deliveryDate,
    notes: order.notes
  }

  // Scroll to form
  document.querySelector('form')?.scrollIntoView({ behavior: 'smooth' })
}

const reorderItem = (order: any) => {
  orderForm.value = {
    potatoType: order.potatoType,
    cutShape: order.cutShape,
    bags: order.bags,
    deliveryDate: '',
    notes: order.notes
  }

  // Scroll to form
  document.querySelector('form')?.scrollIntoView({ behavior: 'smooth' })
}

const viewOrderDetails = (order: any) => {
  alert(`Order Details:\n\nOrder ID: ${order.id}\nProduct: ${order.potatoType} - ${order.cutShape}\nQuantity: ${order.bags} bags (${order.bags * 25}kg)\nDelivery: ${formatDate(order.deliveryDate)}\nStatus: ${getStatusText(order.status)}\nPrice: $${order.price}\nNotes: ${order.notes}\nCreated: ${formatDate(order.createdAt)}`)
}

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    in_production: 'bg-blue-100 text-blue-800',
    completed: 'bg-purple-100 text-purple-800',
    delivered: 'bg-indigo-100 text-indigo-800',
    deleted: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '⏳ Pending Approval',
    approved: '✅ Approved',
    rejected: '❌ Rejected',
    in_production: '🏭 In Production',
    completed: '✅ Completed',
    delivered: '🚚 Delivered',
    deleted: '🗑️ Cancelled'
  }
  return texts[status as keyof typeof texts] || status
}

const getTimeLeft = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'Overdue'
  if (days === 0) return 'Today'
  if (days === 1) return 'Tomorrow'
  return `${days} days`
}

const getUrgencyClass = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'text-red-600 font-bold'
  if (days <= 1) return 'text-orange-600 font-medium'
  if (days <= 3) return 'text-yellow-600'
  return 'text-gray-500'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Set default delivery date to tomorrow
onMounted(() => {
  orderForm.value.deliveryDate = minDate.value
})
</script>
