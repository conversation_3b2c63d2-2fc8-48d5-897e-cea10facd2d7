import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Lazy load views for better performance
const Home = () => import('@/views/Home.vue')
const Login = () => import('@/views/auth/Login.vue')
const Register = () => import('@/views/auth/Register.vue')

// Client Dashboard Views
const ClientDashboard = () => import('@/views/client/Dashboard.vue')
const NewOrder = () => import('@/views/client/NewOrder.vue')
const OrderHistory = () => import('@/views/client/OrderHistory.vue')
const ClientProfile = () => import('@/views/client/Profile.vue')

// Factory Dashboard Views
const FactoryDashboard = () => import('@/views/factory/Dashboard.vue')
const OrderManagement = () => import('@/views/factory/OrderManagement.vue')
const ClientManagement = () => import('@/views/factory/ClientManagement.vue')
const Analytics = () => import('@/views/factory/Analytics.vue')
const AdminPanel = () => import('@/views/factory/AdminPanel.vue')

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home
    },
    {
      path: '/login',
      name: 'login',
      component: Login,
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'register',
      component: Register,
      meta: { requiresGuest: true }
    },
    {
      path: '/client',
      name: 'client',
      component: ClientDashboard,
      meta: { requiresAuth: true, role: 'client' },
      children: [
        {
          path: '',
          name: 'client-dashboard',
          component: ClientDashboard
        },
        {
          path: 'new-order',
          name: 'new-order',
          component: NewOrder
        },
        {
          path: 'orders',
          name: 'order-history',
          component: OrderHistory
        },
        {
          path: 'profile',
          name: 'client-profile',
          component: ClientProfile
        }
      ]
    },
    {
      path: '/factory',
      name: 'factory',
      component: FactoryDashboard,
      meta: { requiresAuth: true, role: 'factory' },
      children: [
        {
          path: '',
          name: 'factory-dashboard',
          component: FactoryDashboard
        },
        {
          path: 'orders',
          name: 'order-management',
          component: OrderManagement
        },
        {
          path: 'clients',
          name: 'client-management',
          component: ClientManagement
        },
        {
          path: 'analytics',
          name: 'analytics',
          component: Analytics
        },
        {
          path: 'admin',
          name: 'admin-panel',
          component: AdminPanel,
          meta: { requiresAuth: true, role: 'admin' }
        }
      ]
    }
  ]
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next('/login')
      return
    }
    
    // Check role-based access
    if (to.meta.role && !authStore.hasRole(to.meta.role as string)) {
      next('/') // Redirect to home if user doesn't have required role
      return
    }
  }
  
  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

export default router
