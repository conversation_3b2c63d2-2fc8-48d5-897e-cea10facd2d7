import { createRouter, createWebHistory } from 'vue-router'
import SimpleLogin from '@/components/SimpleLogin.vue'
import { useSimpleAuthStore } from '@/stores/simpleAuth'

const routes = [
  {
    path: '/',
    name: 'login',
    component: SimpleLogin
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => {
      const authStore = useSimpleAuthStore()
      const userRole = authStore.userRole

      switch (userRole) {
        case 'client':
          return import('@/components/dashboards/ClientDashboard.vue')
        case 'admin':
          return import('@/components/dashboards/AdminDashboard.vue')
        case 'sales_manager':
          return import('@/components/dashboards/SalesManagerDashboard.vue')
        case 'ops_manager':
          return import('@/components/dashboards/OpsManagerDashboard.vue')
        case 'general_manager':
          return import('@/components/dashboards/AdminDashboard.vue') // Same as admin for now
        default:
          return import('@/components/dashboards/ClientDashboard.vue')
      }
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
