import { createRouter, createWebHistory } from 'vue-router'
import SimpleLogin from '@/components/SimpleLogin.vue'

const routes = [
  {
    path: '/',
    name: 'login',
    component: SimpleLogin
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('@/components/SimpleDashboard.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
