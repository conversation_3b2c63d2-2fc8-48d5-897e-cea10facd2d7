import { createRouter, createWebHistory } from 'vue-router'
import SimpleLogin from '@/components/SimpleLogin.vue'
import { useSimpleAuthStore } from '@/stores/simpleAuth'

const routes = [
  {
    path: '/',
    name: 'login',
    component: SimpleLogin
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => {
      const authStore = useSimpleAuthStore()
      const userRole = authStore.userRole

      switch (userRole) {
        case 'client':
          return import('@/components/dashboards/EnhancedClientDashboard.vue')
        case 'admin':
          return import('@/components/dashboards/EnhancedAdminDashboard.vue')
        case 'sales_manager':
          return import('@/components/dashboards/EnhancedSalesManagerDashboard.vue')
        case 'ops_manager':
          return import('@/components/dashboards/EnhancedOpsManagerDashboard.vue')
        case 'general_manager':
          return import('@/components/dashboards/EnhancedGeneralManagerDashboard.vue')
        default:
          return import('@/components/dashboards/EnhancedClientDashboard.vue')
      }
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
