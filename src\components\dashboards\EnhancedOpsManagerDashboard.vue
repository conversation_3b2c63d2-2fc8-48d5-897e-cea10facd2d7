<template>
  <div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">
    <!-- Enhanced Header -->
    <nav class="bg-white/90 backdrop-blur-sm shadow-lg border-b border-indigo-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-10 w-10 bg-gradient-to-r from-indigo-600 to-purple-700 rounded-xl flex items-center justify-center shadow-lg">
              <span class="text-white font-bold text-lg">🏭</span>
            </div>
            <div class="ml-3">
              <span class="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Verto</span>
              <div class="text-sm text-indigo-600 font-medium">Factory Operations</div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <!-- Production Status -->
            <div class="flex items-center space-x-2 bg-indigo-50 px-3 py-1 rounded-full border border-indigo-200">
              <div class="h-2 w-2 bg-indigo-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-indigo-700 font-medium">{{ productionOrders.length }} In Production</span>
            </div>
            <!-- Real-time indicator -->
            <div class="flex items-center space-x-2 bg-green-50 px-3 py-1 rounded-full">
              <div class="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-xs text-green-700 font-medium">Live Feed</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">{{ user?.name }}</div>
              <div class="text-xs text-gray-500">Operations Manager</div>
            </div>
            <span class="bg-gradient-to-r from-indigo-100 to-purple-200 text-indigo-800 px-4 py-2 rounded-full font-medium text-sm shadow-sm">
              🏭 Operations
            </span>
            <button @click="logout" class="bg-red-50 hover:bg-red-100 text-red-600 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md">
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      
      <!-- Factory Overview -->
      <div class="mb-8">
        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-6 text-white shadow-xl">
          <div class="flex justify-between items-center">
            <div>
              <h1 class="text-2xl font-bold mb-2">Factory Operations Center 🏭</h1>
              <p class="text-indigo-100">Real-time production monitoring and order management</p>
            </div>
            <div class="text-right">
              <div class="text-3xl font-bold">{{ productionMetrics.equipmentUtilization }}%</div>
              <div class="text-indigo-200 text-sm">Equipment Utilization</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Production Metrics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg">
              <span class="text-2xl">🏭</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">In Production</p>
              <p class="text-2xl font-bold text-blue-600">{{ productionOrders.length }}</p>
              <p class="text-xs text-gray-500">Active orders</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-lg">
              <span class="text-2xl">✅</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Completed Today</p>
              <p class="text-2xl font-bold text-green-600">{{ productionMetrics.completedToday }}</p>
              <p class="text-xs text-gray-500">Orders finished</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-purple-100 rounded-lg">
              <span class="text-2xl">📦</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Bags Processed</p>
              <p class="text-2xl font-bold text-purple-600">{{ productionMetrics.totalBagsToday }}</p>
              <p class="text-xs text-gray-500">Today's output</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div class="flex items-center">
            <div class="p-3 bg-yellow-100 rounded-lg">
              <span class="text-2xl">⭐</span>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Quality Score</p>
              <p class="text-2xl font-bold text-yellow-600">{{ productionMetrics.qualityScore.toFixed(1) }}%</p>
              <p class="text-xs text-gray-500">Average rating</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Production Queue -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- Ready for Production -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div class="bg-gradient-to-r from-green-500 to-emerald-500 px-6 py-4">
            <h2 class="text-lg font-bold text-white flex items-center">
              <span class="text-xl mr-2">🟢</span>
              Ready for Production
            </h2>
            <p class="text-green-100 text-sm mt-1">{{ readyOrders.length }} orders approved</p>
          </div>
          
          <div class="p-4 max-h-96 overflow-y-auto">
            <div v-if="readyOrders.length === 0" class="text-center py-8 text-gray-500">
              <div class="text-4xl mb-2">✅</div>
              <p>No orders waiting</p>
            </div>
            <div v-else class="space-y-3">
              <div v-for="order in readyOrders" :key="order.id" 
                   class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer"
                   @click="viewOrderDetails(order)">
                <div class="flex justify-between items-start mb-2">
                  <div class="font-medium text-gray-900">#{{ order.id }}</div>
                  <span :class="getPriorityClass(order)" class="px-2 py-1 text-xs font-bold rounded-full">
                    {{ getPriority(order) }}
                  </span>
                </div>
                <div class="text-sm text-gray-600">{{ order.potatoType }} - {{ order.cutShape }}</div>
                <div class="text-sm text-gray-500">{{ order.bags }} bags • {{ order.clientName }}</div>
                <div class="flex justify-between items-center mt-2">
                  <div class="text-xs text-gray-500">Due: {{ formatDate(order.deliveryDate) }}</div>
                  <button @click.stop="startProduction(order)" 
                          class="bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-1 rounded-md font-medium transition-colors">
                    Start
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- In Production -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div class="bg-gradient-to-r from-blue-500 to-indigo-500 px-6 py-4">
            <h2 class="text-lg font-bold text-white flex items-center">
              <span class="text-xl mr-2">🔵</span>
              In Production
            </h2>
            <p class="text-blue-100 text-sm mt-1">{{ productionOrders.length }} orders active</p>
          </div>
          
          <div class="p-4 max-h-96 overflow-y-auto">
            <div v-if="productionOrders.length === 0" class="text-center py-8 text-gray-500">
              <div class="text-4xl mb-2">🏭</div>
              <p>No active production</p>
            </div>
            <div v-else class="space-y-3">
              <div v-for="order in productionOrders" :key="order.id" 
                   class="border border-blue-200 rounded-lg p-3 bg-blue-50 hover:shadow-md transition-shadow cursor-pointer"
                   @click="viewOrderDetails(order)">
                <div class="flex justify-between items-start mb-2">
                  <div class="font-medium text-gray-900">#{{ order.id }}</div>
                  <div class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                    {{ order.productionStatus || 'In Progress' }}
                  </div>
                </div>
                <div class="text-sm text-gray-600">{{ order.potatoType }} - {{ order.cutShape }}</div>
                <div class="text-sm text-gray-500">{{ order.bags }} bags • {{ order.clientName }}</div>
                
                <!-- Progress Bar -->
                <div class="mt-2">
                  <div class="flex justify-between text-xs text-gray-500 mb-1">
                    <span>Progress</span>
                    <span>{{ getProductionProgress(order) }}%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                         :style="{ width: getProductionProgress(order) + '%' }"></div>
                  </div>
                </div>
                
                <div class="flex justify-between items-center mt-2">
                  <div class="text-xs text-gray-500">ETA: {{ getEstimatedCompletion(order) }}</div>
                  <button @click.stop="completeProduction(order)" 
                          class="bg-purple-600 hover:bg-purple-700 text-white text-xs px-3 py-1 rounded-md font-medium transition-colors">
                    Complete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Completed Today -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div class="bg-gradient-to-r from-purple-500 to-pink-500 px-6 py-4">
            <h2 class="text-lg font-bold text-white flex items-center">
              <span class="text-xl mr-2">🟣</span>
              Completed Today
            </h2>
            <p class="text-purple-100 text-sm mt-1">{{ completedToday.length }} orders finished</p>
          </div>
          
          <div class="p-4 max-h-96 overflow-y-auto">
            <div v-if="completedToday.length === 0" class="text-center py-8 text-gray-500">
              <div class="text-4xl mb-2">📦</div>
              <p>No completions yet</p>
            </div>
            <div v-else class="space-y-3">
              <div v-for="order in completedToday" :key="order.id" 
                   class="border border-purple-200 rounded-lg p-3 bg-purple-50 hover:shadow-md transition-shadow cursor-pointer"
                   @click="viewOrderDetails(order)">
                <div class="flex justify-between items-start mb-2">
                  <div class="font-medium text-gray-900">#{{ order.id }}</div>
                  <div class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full font-medium">
                    ⭐ {{ order.qualityScore || 95 }}%
                  </div>
                </div>
                <div class="text-sm text-gray-600">{{ order.potatoType }} - {{ order.cutShape }}</div>
                <div class="text-sm text-gray-500">{{ order.bags }} bags • {{ order.clientName }}</div>
                <div class="flex justify-between items-center mt-2">
                  <div class="text-xs text-gray-500">
                    Completed: {{ formatTime(order.updatedAt) }}
                  </div>
                  <div class="text-xs text-purple-600 font-medium">
                    {{ order.actualProductionTime?.toFixed(1) || 'N/A' }}h
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Orders Table -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-gray-800 to-gray-900 px-6 py-4 flex justify-between items-center">
          <div>
            <h2 class="text-xl font-bold text-white flex items-center">
              <span class="text-2xl mr-2">📋</span>
              Detailed Order Management
            </h2>
            <p class="text-gray-300 text-sm mt-1">Complete order details and production tracking</p>
          </div>
          <div class="flex space-x-3">
            <select v-model="statusFilter" class="bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 text-sm">
              <option value="">All Orders</option>
              <option value="approved">Ready for Production</option>
              <option value="in_production">In Production</option>
              <option value="completed">Completed</option>
            </select>
            <button @click="exportProductionReport" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              📊 Export Report
            </button>
          </div>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Details</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Specifications</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Production Info</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timeline</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="order in filteredOrders" :key="order.id" class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
                  <div class="text-sm text-gray-500">{{ order.clientName }}</div>
                  <div class="text-xs text-gray-400">{{ order.clientType }}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900">{{ order.potatoType }} Potatoes</div>
                  <div class="text-sm text-gray-500">Cut: {{ order.cutShape }}</div>
                  <div class="text-sm text-gray-500">{{ order.bags }} bags ({{ order.bags * 25 }}kg)</div>
                  <div v-if="order.notes" class="text-xs text-gray-400 mt-1 max-w-xs truncate">{{ order.notes }}</div>
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900">Est: {{ order.estimatedProductionTime }}h</div>
                  <div v-if="order.actualProductionTime" class="text-sm text-gray-500">
                    Actual: {{ order.actualProductionTime.toFixed(1) }}h
                  </div>
                  <div v-if="order.qualityScore" class="text-sm text-green-600">
                    Quality: {{ order.qualityScore }}%
                  </div>
                  <div class="text-xs" :class="getPriorityClass(order)">{{ getPriority(order) }} Priority</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">Due: {{ formatDate(order.deliveryDate) }}</div>
                  <div class="text-sm" :class="getUrgencyClass(order.deliveryDate)">{{ getTimeLeft(order.deliveryDate) }}</div>
                  <div class="text-xs text-gray-500">Created: {{ formatDate(order.createdAt) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(order.status)" class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full">
                    {{ getStatusText(order.status) }}
                  </span>
                  <div v-if="order.productionStatus" class="text-xs text-gray-500 mt-1">
                    {{ order.productionStatus }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button v-if="order.status === 'approved'" @click="startProduction(order)" 
                          class="text-green-600 hover:text-green-900 font-medium">Start</button>
                  <button v-if="order.status === 'in_production'" @click="completeProduction(order)" 
                          class="text-purple-600 hover:text-purple-900 font-medium">Complete</button>
                  <button @click="viewOrderDetails(order)" 
                          class="text-gray-600 hover:text-gray-900 font-medium">Details</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'
import { useOrderStore } from '@/stores/orderStore'

const router = useRouter()
const authStore = useSimpleAuthStore()
const orderStore = useOrderStore()
const { user } = authStore

// State
const statusFilter = ref('')

// Computed properties
const productionOrders = computed(() => orderStore.productionOrders)
const productionMetrics = computed(() => orderStore.productionMetrics)

const readyOrders = computed(() =>
  orderStore.orders.filter(o => o.status === 'approved').sort((a, b) => {
    // Sort by priority and delivery date
    const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 }
    const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 2
    const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 2

    if (aPriority !== bPriority) return aPriority - bPriority
    return new Date(a.deliveryDate).getTime() - new Date(b.deliveryDate).getTime()
  })
)

const completedToday = computed(() => {
  const today = new Date().toDateString()
  return orderStore.orders.filter(o =>
    o.status === 'completed' &&
    new Date(o.updatedAt).toDateString() === today
  ).sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
})

const filteredOrders = computed(() => {
  let orders = orderStore.orders.filter(o =>
    ['approved', 'in_production', 'completed'].includes(o.status)
  )

  if (statusFilter.value) {
    orders = orders.filter(o => o.status === statusFilter.value)
  }

  return orders.sort((a, b) => {
    // Sort by status priority, then by delivery date
    const statusOrder = { in_production: 0, approved: 1, completed: 2 }
    const aStatus = statusOrder[a.status as keyof typeof statusOrder] || 3
    const bStatus = statusOrder[b.status as keyof typeof statusOrder] || 3

    if (aStatus !== bStatus) return aStatus - bStatus
    return new Date(a.deliveryDate).getTime() - new Date(b.deliveryDate).getTime()
  })
})

// Methods
const logout = () => {
  authStore.logout()
  router.push('/')
}

const startProduction = async (order: any) => {
  try {
    await orderStore.startProduction(order.id)
    console.log(`🏭 Production started for order ${order.id}`)
  } catch (error) {
    alert('❌ Failed to start production. Please try again.')
  }
}

const completeProduction = async (order: any) => {
  const qualityScore = prompt('Enter quality score (0-100):', '95')
  if (qualityScore && !isNaN(Number(qualityScore))) {
    try {
      await orderStore.completeProduction(order.id, Number(qualityScore))
      console.log(`✅ Production completed for order ${order.id} with quality score ${qualityScore}%`)
    } catch (error) {
      alert('❌ Failed to complete production. Please try again.')
    }
  }
}

const viewOrderDetails = (order: any) => {
  const details = `
DETAILED ORDER INFORMATION

Order ID: ${order.id}
Client: ${order.clientName} (${order.clientType})

PRODUCT SPECIFICATIONS:
• Potato Type: ${order.potatoType}
• Cut Shape: ${order.cutShape}
• Quantity: ${order.bags} bags (${order.bags * 25}kg total)
• Special Notes: ${order.notes || 'None'}

PRODUCTION DETAILS:
• Priority: ${getPriority(order)}
• Estimated Time: ${order.estimatedProductionTime} hours
${order.actualProductionTime ? `• Actual Time: ${order.actualProductionTime.toFixed(1)} hours` : ''}
${order.qualityScore ? `• Quality Score: ${order.qualityScore}%` : ''}
• Production Status: ${order.productionStatus || 'Not started'}

TIMELINE:
• Order Created: ${formatDate(order.createdAt)}
• Delivery Due: ${formatDate(order.deliveryDate)} (${getTimeLeft(order.deliveryDate)})
• Last Updated: ${formatDate(order.updatedAt)}
${order.approvedAt ? `• Approved: ${formatDate(order.approvedAt)}` : ''}

STATUS: ${getStatusText(order.status)}
PRICE: $${order.price}
  `
  alert(details)
}

const exportProductionReport = () => {
  const productionData = filteredOrders.value

  const csvContent = [
    ['Order ID', 'Client', 'Product', 'Cut Shape', 'Bags', 'Weight (kg)', 'Status', 'Production Status', 'Priority', 'Estimated Time', 'Actual Time', 'Quality Score', 'Delivery Date', 'Created Date'].join(','),
    ...productionData.map(order => [
      order.id,
      order.clientName,
      order.potatoType,
      order.cutShape,
      order.bags,
      order.bags * 25,
      order.status,
      order.productionStatus || '',
      order.priority,
      order.estimatedProductionTime,
      order.actualProductionTime || '',
      order.qualityScore || '',
      order.deliveryDate,
      order.createdAt
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `production-report-${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
}

const getPriority = (order: any) => {
  const deliveryDays = Math.ceil((new Date(order.deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (deliveryDays <= 1) return 'URGENT'
  if (deliveryDays <= 3) return 'HIGH'
  if (order.bags >= 100) return 'HIGH'
  return 'NORMAL'
}

const getPriorityClass = (order: any) => {
  const priority = getPriority(order)
  const classes = {
    URGENT: 'bg-red-100 text-red-800',
    HIGH: 'bg-orange-100 text-orange-800',
    NORMAL: 'bg-blue-100 text-blue-800'
  }
  return classes[priority as keyof typeof classes]
}

const getProductionProgress = (order: any) => {
  // Simulate production progress based on time elapsed
  const startTime = new Date(order.updatedAt).getTime()
  const currentTime = new Date().getTime()
  const elapsedHours = (currentTime - startTime) / (1000 * 60 * 60)
  const progress = Math.min((elapsedHours / order.estimatedProductionTime) * 100, 95)
  return Math.round(progress)
}

const getEstimatedCompletion = (order: any) => {
  const startTime = new Date(order.updatedAt)
  const completionTime = new Date(startTime.getTime() + (order.estimatedProductionTime * 60 * 60 * 1000))
  const now = new Date()

  if (completionTime <= now) return 'Overdue'

  const hoursLeft = Math.ceil((completionTime.getTime() - now.getTime()) / (1000 * 60 * 60))
  if (hoursLeft < 1) return 'Less than 1h'
  if (hoursLeft === 1) return '1 hour'
  return `${hoursLeft} hours`
}

const getStatusClass = (status: string) => {
  const classes = {
    approved: 'bg-green-100 text-green-800',
    in_production: 'bg-blue-100 text-blue-800',
    completed: 'bg-purple-100 text-purple-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    approved: '🟢 Ready',
    in_production: '🔵 Production',
    completed: '🟣 Completed'
  }
  return texts[status as keyof typeof texts] || status
}

const getTimeLeft = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'Overdue'
  if (days === 0) return 'Today'
  if (days === 1) return 'Tomorrow'
  return `${days} days`
}

const getUrgencyClass = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days < 0) return 'text-red-600 font-bold'
  if (days <= 1) return 'text-orange-600 font-medium'
  if (days <= 3) return 'text-yellow-600'
  return 'text-gray-500'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatTime = (date: string) => {
  return new Date(date).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
