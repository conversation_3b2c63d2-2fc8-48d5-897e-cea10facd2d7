<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-orange-600 rounded-md flex items-center justify-center">
              <span class="text-white font-bold text-sm">V</span>
            </div>
            <span class="ml-2 text-xl font-semibold text-gray-900">Verto</span>
            <span class="ml-2 text-sm text-gray-500">Factory Operations</span>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-gray-700">{{ user?.name }}</span>
            <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">Ops Manager</span>
            <button @click="logout" class="text-blue-600 hover:text-blue-800">Logout</button>
          </div>
        </div>
      </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        
        <!-- Production Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Ready to Produce</h3>
            <p class="text-3xl font-bold text-green-600">{{ readyToProduce.length }}</p>
            <p class="text-sm text-gray-500">Approved orders</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">In Production</h3>
            <p class="text-3xl font-bold text-blue-600">{{ inProduction.length }}</p>
            <p class="text-sm text-gray-500">Currently processing</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Total Bags Today</h3>
            <p class="text-3xl font-bold text-orange-600">{{ totalBagsToday }}</p>
            <p class="text-sm text-gray-500">Production target</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Urgent Orders</h3>
            <p class="text-3xl font-bold text-red-600">{{ urgentOrders.length }}</p>
            <p class="text-sm text-gray-500">Delivery ≤ 2 days</p>
          </div>
        </div>

        <!-- Production Queue -->
        <div class="bg-white rounded-lg shadow mb-8">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">🏭 Production Queue</h2>
            <p class="text-sm text-gray-600 mt-1">Orders ready for factory production</p>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Specs</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="order in productionQueue" :key="order.id" :class="getPriorityRowClass(order)">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getPriorityClass(order)" class="px-2 py-1 text-xs font-bold rounded-full">
                      {{ getPriority(order) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">#{{ order.id }}</div>
                    <div class="text-sm text-gray-500">{{ order.clientName }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ order.potatoType }}</div>
                    <div class="text-sm text-gray-500">Cut: {{ order.cutShape }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ order.bags }} bags</div>
                    <div class="text-sm text-gray-500">~{{ order.bags * 25 }}kg</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ formatDate(order.deliveryDate) }}</div>
                    <div class="text-sm" :class="getUrgencyClass(order.deliveryDate)">{{ getTimeLeft(order.deliveryDate) }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getStatusClass(order.productionStatus || 'ready')" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                      {{ order.productionStatus || 'ready' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button v-if="!order.productionStatus || order.productionStatus === 'ready'" 
                            @click="startProduction(order)" 
                            class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs font-medium">
                      🚀 Start
                    </button>
                    <button v-if="order.productionStatus === 'in_progress'" 
                            @click="completeProduction(order)" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium">
                      ✅ Complete
                    </button>
                    <button @click="viewProductionDetails(order)" 
                            class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs font-medium">
                      📋 Details
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Production Summary -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Daily Production Plan -->
          <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">📅 Today's Production Plan</h2>
            </div>
            <div class="p-6">
              <div class="space-y-4">
                <div v-for="plan in dailyPlan" :key="plan.type" class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 class="font-medium text-gray-900">{{ plan.type }}</h4>
                    <p class="text-sm text-gray-500">{{ plan.cuts.join(', ') }}</p>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-bold text-gray-900">{{ plan.bags }} bags</div>
                    <div class="text-sm text-gray-500">{{ plan.weight }}kg</div>
                  </div>
                </div>
              </div>
              <div class="mt-6 pt-4 border-t border-gray-200">
                <div class="flex justify-between items-center">
                  <span class="font-medium text-gray-900">Total Production</span>
                  <span class="text-xl font-bold text-orange-600">{{ totalBagsToday }} bags</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Equipment Status -->
          <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">⚙️ Equipment Status</h2>
            </div>
            <div class="p-6">
              <div class="space-y-4">
                <div v-for="equipment in equipmentStatus" :key="equipment.name" class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 class="font-medium text-gray-900">{{ equipment.name }}</h4>
                    <p class="text-sm text-gray-500">{{ equipment.description }}</p>
                  </div>
                  <div class="text-right">
                    <span :class="getEquipmentStatusClass(equipment.status)" class="px-2 py-1 text-xs font-bold rounded-full">
                      {{ equipment.status }}
                    </span>
                    <div class="text-sm text-gray-500 mt-1">{{ equipment.utilization }}%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useSimpleAuthStore } from '@/stores/simpleAuth'

const router = useRouter()
const authStore = useSimpleAuthStore()
const { user } = authStore

// Sample production data
const allOrders = ref([
  {
    id: '001',
    clientName: 'Restaurant ABC',
    potatoType: 'russet',
    cutShape: 'cubes',
    bags: 50,
    deliveryDate: '2024-01-15',
    status: 'approved',
    productionStatus: 'ready',
    createdAt: '2024-01-10',
    price: 250
  },
  {
    id: '003',
    clientName: 'Hotel DEF',
    potatoType: 'yukon',
    cutShape: 'long',
    bags: 100,
    deliveryDate: '2024-01-16',
    status: 'approved',
    productionStatus: 'in_progress',
    createdAt: '2024-01-08',
    price: 500
  },
  {
    id: '006',
    clientName: 'Fast Food Chain',
    potatoType: 'russet',
    cutShape: 'long',
    bags: 200,
    deliveryDate: '2024-01-14',
    status: 'approved',
    productionStatus: 'ready',
    createdAt: '2024-01-12',
    price: 1000
  }
])

const dailyPlan = ref([
  {
    type: 'Russet Potatoes',
    cuts: ['cubes', 'long strips'],
    bags: 250,
    weight: 6250
  },
  {
    type: 'Yukon Gold',
    cuts: ['long strips', 'wedges'],
    bags: 100,
    weight: 2500
  },
  {
    type: 'Red Potatoes',
    cuts: ['wedges', 'whole'],
    bags: 75,
    weight: 1875
  }
])

const equipmentStatus = ref([
  {
    name: 'Washing Line A',
    description: 'Primary potato washing',
    status: 'operational',
    utilization: 85
  },
  {
    name: 'Cutting Machine 1',
    description: 'Cubes & strips cutting',
    status: 'operational',
    utilization: 92
  },
  {
    name: 'Cutting Machine 2',
    description: 'Wedges & specialty cuts',
    status: 'maintenance',
    utilization: 0
  },
  {
    name: 'Packaging Line',
    description: 'Bagging and sealing',
    status: 'operational',
    utilization: 78
  }
])

// Computed properties
const productionQueue = computed(() => allOrders.value.filter(o => o.status === 'approved'))
const readyToProduce = computed(() => productionQueue.value.filter(o => !o.productionStatus || o.productionStatus === 'ready'))
const inProduction = computed(() => productionQueue.value.filter(o => o.productionStatus === 'in_progress'))
const urgentOrders = computed(() => {
  const twoDaysFromNow = new Date()
  twoDaysFromNow.setDate(twoDaysFromNow.getDate() + 2)
  return productionQueue.value.filter(o => new Date(o.deliveryDate) <= twoDaysFromNow)
})
const totalBagsToday = computed(() => dailyPlan.value.reduce((sum, plan) => sum + plan.bags, 0))

// Methods
const logout = () => {
  authStore.logout()
  router.push('/')
}

const startProduction = (order: any) => {
  if (confirm(`Start production for order #${order.id}?`)) {
    order.productionStatus = 'in_progress'
    order.productionStarted = new Date().toISOString()
    alert(`🚀 Production started for order #${order.id}`)
  }
}

const completeProduction = (order: any) => {
  if (confirm(`Mark production complete for order #${order.id}?`)) {
    order.productionStatus = 'completed'
    order.productionCompleted = new Date().toISOString()
    order.status = 'ready_for_delivery'
    alert(`✅ Production completed for order #${order.id}`)
  }
}

const viewProductionDetails = (order: any) => {
  alert(`Production Details - Order #${order.id}:\n\nProduct: ${order.potatoType} - ${order.cutShape}\nQuantity: ${order.bags} bags (~${order.bags * 25}kg)\nClient: ${order.clientName}\nDelivery: ${order.deliveryDate}\nStatus: ${order.productionStatus || 'ready'}\n\nProduction Notes:\n- Estimated time: ${Math.ceil(order.bags / 50)} hours\n- Equipment needed: Washing Line A, Cutting Machine\n- Quality check required before packaging`)
}

const getPriority = (order: any) => {
  const days = Math.ceil((new Date(order.deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days <= 1) return 'URGENT'
  if (days <= 2) return 'HIGH'
  if (days <= 3) return 'MEDIUM'
  return 'LOW'
}

const getPriorityClass = (order: any) => {
  const priority = getPriority(order)
  const classes = {
    'URGENT': 'bg-red-100 text-red-800',
    'HIGH': 'bg-orange-100 text-orange-800',
    'MEDIUM': 'bg-yellow-100 text-yellow-800',
    'LOW': 'bg-green-100 text-green-800'
  }
  return classes[priority as keyof typeof classes]
}

const getPriorityRowClass = (order: any) => {
  const priority = getPriority(order)
  if (priority === 'URGENT') return 'bg-red-50 border-l-4 border-red-500'
  if (priority === 'HIGH') return 'bg-orange-50 border-l-4 border-orange-500'
  return ''
}

const getTimeLeft = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days <= 0) return 'OVERDUE'
  if (days === 1) return 'Tomorrow'
  return `${days} days`
}

const getUrgencyClass = (deliveryDate: string) => {
  const days = Math.ceil((new Date(deliveryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  if (days <= 0) return 'text-red-600 font-bold'
  if (days <= 1) return 'text-red-600 font-bold'
  if (days <= 2) return 'text-orange-600'
  return 'text-gray-500'
}

const getStatusClass = (status: string) => {
  const classes = {
    ready: 'bg-yellow-100 text-yellow-800',
    in_progress: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    ready_for_delivery: 'bg-purple-100 text-purple-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getEquipmentStatusClass = (status: string) => {
  const classes = {
    operational: 'bg-green-100 text-green-800',
    maintenance: 'bg-red-100 text-red-800',
    idle: 'bg-gray-100 text-gray-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}
</script>
