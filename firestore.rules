rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }
    
    function isClient() {
      return getUserRole() == 'client';
    }
    
    function isFactoryStaff() {
      return getUserRole() in ['sales_manager', 'general_manager', 'ops_manager', 'admin'];
    }
    
    function isSalesManager() {
      return getUserRole() == 'sales_manager';
    }
    
    function isAdmin() {
      return getUserRole() == 'admin';
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    // Users collection
    match /users/{userId} {
      // Users can read their own profile
      allow read: if isAuthenticated() && isOwner(userId);
      
      // Users can update their own profile (except role and sensitive fields)
      allow update: if isAuthenticated() && isOwner(userId) 
        && !('role' in request.resource.data.diff(resource.data).affectedKeys())
        && !('isActive' in request.resource.data.diff(resource.data).affectedKeys());
      
      // Only admins can create users and modify roles
      allow create: if isAuthenticated() && isAdmin();
      allow update: if isAuthenticated() && isAdmin();
      
      // Factory staff can read client profiles
      allow read: if isAuthenticated() && isFactoryStaff() && resource.data.role == 'client';
    }

    // Orders collection
    match /orders/{orderId} {
      // Clients can read their own orders
      allow read: if isAuthenticated() && isClient() && resource.data.clientId == request.auth.uid;
      
      // Clients can create orders
      allow create: if isAuthenticated() && isClient() 
        && request.resource.data.clientId == request.auth.uid
        && request.resource.data.status == 'pending';
      
      // Clients can update their own pending orders
      allow update: if isAuthenticated() && isClient() 
        && resource.data.clientId == request.auth.uid
        && resource.data.status == 'pending'
        && request.resource.data.status == 'pending';
      
      // Factory staff can read all orders
      allow read: if isAuthenticated() && isFactoryStaff();
      
      // Sales managers can approve/reject orders
      allow update: if isAuthenticated() && isSalesManager()
        && request.resource.data.status in ['approved', 'rejected']
        && resource.data.status == 'pending';
      
      // Factory staff can update order status and internal notes
      allow update: if isAuthenticated() && isFactoryStaff()
        && request.resource.data.status in ['delivered', 'cancelled'];
    }

    // Products collection
    match /products/{productId} {
      // Everyone can read active products
      allow read: if isAuthenticated() && resource.data.isActive == true;
      
      // Only admins can manage products
      allow create, update, delete: if isAuthenticated() && isAdmin();
    }

    // Clients collection (for client management)
    match /clients/{clientId} {
      // Clients can read their own profile
      allow read: if isAuthenticated() && isClient() && clientId == request.auth.uid;
      
      // Factory staff can read all client profiles
      allow read: if isAuthenticated() && isFactoryStaff();
      
      // Factory staff can update client tags and notes
      allow update: if isAuthenticated() && isFactoryStaff();
      
      // Only admins can create/delete client records
      allow create, delete: if isAuthenticated() && isAdmin();
    }

    // Logs collection (for audit trail)
    match /logs/{logId} {
      // Only factory staff can read logs
      allow read: if isAuthenticated() && isFactoryStaff();
      
      // System can create logs (via Firebase Functions)
      allow create: if true;
    }

    // Integration placeholders (for future WhatsApp integration)
    match /integrations/{integrationId} {
      // Only admins can manage integrations
      allow read, write: if isAuthenticated() && isAdmin();
    }

    // Analytics data
    match /analytics/{analyticsId} {
      // Only factory staff can read analytics
      allow read: if isAuthenticated() && isFactoryStaff();
      
      // System can write analytics (via Firebase Functions)
      allow write: if true;
    }
  }
}
