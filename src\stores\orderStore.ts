import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useSimpleAuthStore } from './simpleAuth'

export interface Order {
  id: string
  clientId: string
  clientName: string
  clientType: string
  potatoType: string
  cutShape: string
  bags: number
  deliveryDate: string
  status: 'pending' | 'approved' | 'rejected' | 'in_production' | 'completed' | 'delivered' | 'deleted'
  productionStatus?: 'ready' | 'in_progress' | 'completed' | 'quality_check' | 'packaged'
  notes: string
  price: number
  createdAt: string
  updatedAt: string
  approvedBy?: string
  approvedAt?: string
  rejectionReason?: string
  flag?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  estimatedProductionTime: number // in hours
  actualProductionTime?: number
  qualityScore?: number
}

export interface ProductionMetrics {
  totalOrders: number
  pendingApproval: number
  inProduction: number
  completedToday: number
  totalBagsToday: number
  averageProductionTime: number
  qualityScore: number
  equipmentUtilization: number
}

export interface SystemMetrics {
  activeUsers: number
  ordersPerHour: number
  systemUptime: number
  responseTime: number
  errorRate: number
  dataIntegrity: number
}

export const useOrderStore = defineStore('orders', () => {
  const authStore = useSimpleAuthStore()
  
  // State
  const orders = ref<Order[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdate = ref<string>(new Date().toISOString())
  
  // Real-time broadcasting simulation
  const broadcastChannel = ref<BroadcastChannel | null>(null)
  
  // Initialize broadcast channel for real-time updates
  const initBroadcast = () => {
    if (typeof BroadcastChannel !== 'undefined') {
      broadcastChannel.value = new BroadcastChannel('verto-orders')
      broadcastChannel.value.onmessage = (event) => {
        const { type, data } = event.data
        switch (type) {
          case 'ORDER_CREATED':
          case 'ORDER_UPDATED':
            updateOrderInStore(data)
            break
          case 'ORDER_DELETED':
            removeOrderFromStore(data.id)
            break
        }
      }
    }
  }
  
  // Broadcast order changes
  const broadcast = (type: string, data: any) => {
    if (broadcastChannel.value) {
      broadcastChannel.value.postMessage({ type, data, timestamp: new Date().toISOString() })
    }
    lastUpdate.value = new Date().toISOString()
  }
  
  // Sample data initialization
  const initializeOrders = () => {
    const sampleOrders: Order[] = [
      {
        id: 'ORD-001',
        clientId: '1',
        clientName: 'Restaurant ABC',
        clientType: 'Restaurant',
        potatoType: 'russet',
        cutShape: 'cubes',
        bags: 50,
        deliveryDate: '2024-01-15',
        status: 'approved',
        productionStatus: 'ready',
        notes: 'For weekend rush',
        price: 250,
        createdAt: '2024-01-10T10:00:00Z',
        updatedAt: '2024-01-10T10:00:00Z',
        priority: 'high',
        estimatedProductionTime: 4
      },
      {
        id: 'ORD-002',
        clientId: '2',
        clientName: 'Cafe XYZ',
        clientType: 'Cafe',
        potatoType: 'red',
        cutShape: 'wedges',
        bags: 25,
        deliveryDate: '2024-01-20',
        status: 'pending',
        notes: 'First time order',
        price: 125,
        createdAt: '2024-01-12T14:30:00Z',
        updatedAt: '2024-01-12T14:30:00Z',
        priority: 'medium',
        estimatedProductionTime: 2
      },
      {
        id: 'ORD-003',
        clientId: '3',
        clientName: 'Hotel DEF',
        clientType: 'Hotel',
        potatoType: 'yukon',
        cutShape: 'long',
        bags: 100,
        deliveryDate: '2024-01-16',
        status: 'in_production',
        productionStatus: 'in_progress',
        notes: 'Regular weekly order',
        price: 500,
        createdAt: '2024-01-08T09:15:00Z',
        updatedAt: '2024-01-14T11:20:00Z',
        priority: 'urgent',
        estimatedProductionTime: 6,
        actualProductionTime: 4.5
      }
    ]
    
    orders.value = sampleOrders
  }
  
  // Computed properties
  const userOrders = computed(() => {
    if (!authStore.user) return []
    
    switch (authStore.userRole) {
      case 'client':
        return orders.value.filter(order => order.clientId === authStore.user?.id)
      case 'sales_manager':
      case 'admin':
        return orders.value
      case 'ops_manager':
        return orders.value.filter(order => ['approved', 'in_production', 'completed'].includes(order.status))
      case 'general_manager':
        return orders.value
      default:
        return []
    }
  })
  
  const pendingOrders = computed(() => orders.value.filter(o => o.status === 'pending'))
  const approvedOrders = computed(() => orders.value.filter(o => o.status === 'approved'))
  const productionOrders = computed(() => orders.value.filter(o => o.status === 'in_production'))
  const completedOrders = computed(() => orders.value.filter(o => o.status === 'completed'))
  const urgentOrders = computed(() => orders.value.filter(o => o.priority === 'urgent'))
  
  const productionMetrics = computed((): ProductionMetrics => {
    const today = new Date().toDateString()
    const todayOrders = orders.value.filter(o => 
      new Date(o.createdAt).toDateString() === today
    )
    
    return {
      totalOrders: orders.value.length,
      pendingApproval: pendingOrders.value.length,
      inProduction: productionOrders.value.length,
      completedToday: todayOrders.filter(o => o.status === 'completed').length,
      totalBagsToday: todayOrders.reduce((sum, o) => sum + o.bags, 0),
      averageProductionTime: orders.value
        .filter(o => o.actualProductionTime)
        .reduce((sum, o, _, arr) => sum + (o.actualProductionTime || 0) / arr.length, 0),
      qualityScore: orders.value
        .filter(o => o.qualityScore)
        .reduce((sum, o, _, arr) => sum + (o.qualityScore || 0) / arr.length, 0) || 95,
      equipmentUtilization: 85 // Mock data
    }
  })
  
  const systemMetrics = computed((): SystemMetrics => ({
    activeUsers: 12, // Mock data
    ordersPerHour: Math.round(orders.value.length / 24),
    systemUptime: 99.8,
    responseTime: 120,
    errorRate: 0.02,
    dataIntegrity: 99.9
  }))
  
  // Actions
  const createOrder = async (orderData: Partial<Order>) => {
    loading.value = true
    try {
      const newOrder: Order = {
        id: `ORD-${Date.now()}`,
        clientId: authStore.user?.id || '',
        clientName: authStore.user?.name || '',
        clientType: 'Restaurant',
        status: 'pending',
        priority: 'medium',
        estimatedProductionTime: Math.ceil(orderData.bags! / 25),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...orderData
      } as Order
      
      orders.value.push(newOrder)
      broadcast('ORDER_CREATED', newOrder)
      
      // Simulate real-time notification
      setTimeout(() => {
        console.log(`🔔 New order ${newOrder.id} created by ${newOrder.clientName}`)
      }, 100)
      
      return newOrder
    } finally {
      loading.value = false
    }
  }
  
  const updateOrder = async (orderId: string, updates: Partial<Order>) => {
    const orderIndex = orders.value.findIndex(o => o.id === orderId)
    if (orderIndex === -1) throw new Error('Order not found')
    
    const updatedOrder = {
      ...orders.value[orderIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    }
    
    orders.value[orderIndex] = updatedOrder
    broadcast('ORDER_UPDATED', updatedOrder)
    
    return updatedOrder
  }
  
  const approveOrder = async (orderId: string, approverId: string) => {
    return updateOrder(orderId, {
      status: 'approved',
      approvedBy: approverId,
      approvedAt: new Date().toISOString()
    })
  }
  
  const rejectOrder = async (orderId: string, reason: string) => {
    return updateOrder(orderId, {
      status: 'rejected',
      rejectionReason: reason
    })
  }
  
  const startProduction = async (orderId: string) => {
    return updateOrder(orderId, {
      status: 'in_production',
      productionStatus: 'in_progress'
    })
  }
  
  const completeProduction = async (orderId: string, qualityScore: number = 95) => {
    const order = orders.value.find(o => o.id === orderId)
    const actualTime = order ? Math.random() * order.estimatedProductionTime + order.estimatedProductionTime * 0.8 : 0
    
    return updateOrder(orderId, {
      status: 'completed',
      productionStatus: 'completed',
      actualProductionTime: actualTime,
      qualityScore
    })
  }
  
  const deleteOrder = async (orderId: string) => {
    return updateOrder(orderId, {
      status: 'deleted'
    })
  }

  const restoreOrder = async (orderId: string) => {
    return updateOrder(orderId, {
      status: 'pending'
    })
  }
  
  const updateOrderInStore = (order: Order) => {
    const index = orders.value.findIndex(o => o.id === order.id)
    if (index !== -1) {
      orders.value[index] = order
    } else {
      orders.value.push(order)
    }
  }
  
  const removeOrderFromStore = (orderId: string) => {
    const index = orders.value.findIndex(o => o.id === orderId)
    if (index !== -1) {
      orders.value.splice(index, 1)
    }
  }
  
  // Initialize on store creation
  initBroadcast()
  initializeOrders()
  
  return {
    // State
    orders,
    loading,
    error,
    lastUpdate,
    
    // Computed
    userOrders,
    pendingOrders,
    approvedOrders,
    productionOrders,
    completedOrders,
    urgentOrders,
    productionMetrics,
    systemMetrics,
    
    // Actions
    createOrder,
    updateOrder,
    approveOrder,
    rejectOrder,
    startProduction,
    completeProduction,
    deleteOrder,
    restoreOrder
  }
})
